fastapi
pydantic==1.4
uvicorn[standard]==0.30.3
requests==2.32.3
pandas==2.2.2
Openpyxl
pvlib==0.11.0
python-dotenv==1.0.1
sqlalchemy==1.3.16
pymysql==1.1.1
alembic==1.13.2
cryptography==43.0.0
# pydantic_settings==2.3.4
regex==2024.7.24
httpx
pydantic[email]
PyJWT==2.9.0
python-multipart==0.0.9
google-auth==2.32.0
google-auth-oauthlib==1.2.1
stripe==10.12.0
aiofiles
google-analytics-data
tzlocal
pillow
pycountry
pymongo