ORIGI_PVGIS_API_BASE_URLS='http://************:61160' 
ORIGI_PVGIS_API_PATH='/api/{api_version}'
PVGIS_HOURLY_RAD_ROUTE='seriescalc'
PVGIS_DAILY_RAD_ROUTE='DRcalc'
PVGIS_MONTHLY_RAD_ROUTE='MRcalc'
PVGIS_TMY_ROUTE='tmy'
PVGIS_OFFGRID_ROUTE='SHScalc'
PVGIS_GRID_CONNECTED_OR_TRACKING_ROUTE='PVcalc'
PVGIS_HORIZON_PROFILE_ROUTE=printhorizon
PVGIS_DEGREEDAYS_ROUTE='degreedays'
PVGIS_EXTENT_ROUTE='extent'
IP_LOCATION_URL='http://ip-api.com/json'
AUTH_API_URL=https://dev.auth.pvgis.com
NOTIFICATION_API='https://dev.ns.pvgis.com'
SETTINGS_API_URL='https://dev.cfg.pvgis.com'


# ALLOWED_URL
ALLOWED_URL= *, http://localhost:4200

ELASTIC_INDEX='local-pvgis'
ELASTIC_USERNAME='elastic'
ELASTIC_PASSWORD='WKjV6nf1'
LOGSTASH_URL='http://************:9203'

# Mysql Config
MYSQL_HOST='localhost'
MYSQL_PORT=3306
MYSQL_USER='root'
MYSQL_PASSWORD='password'
MYSQL_DATABASE='pvgis_db_test'

# Mongo config
MONGO_HOST='************' # Replace with the actual IP or hostname
MONGO_PORT='27017'  # Replace with the MySQL server's port
MONGO_USER='mongodb_docker_user'
MONGO_PASSWORD='eYVX7EwVmmxKPCDmwMtyKVge8oLd2t81'
MONGO_AUTH_SOURCE='admin'
MONGO_USER_ACTIVITY_COLLECTION='dev_pvgis_user_activity'
MONGO_USER_ACTIVITY_DB='dev_pvgis_user_activity_db'

# Auth registration
APP_KEY='8798654564123'
PVGIS_UI_APP_KEY="8798654564123"
PVGIS_ADMIN_APP_KEY="4512345478963"
PVGIS_UI_URL='http://localhost:3000'
PVGIS_API_BASE_URL= "http://localhost:3000"

#Google Auth Credential
GOOGLE_CLIENT_ID="************-5t0553n1o8cvca8qu2qpnlkhltchvbj5.apps.googleusercontent.com"
GOOGLE_CLIENT_SECRET="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"
GOOGLE_REDIRECT_URI="/auth/google/callback"
GOOGLE_LOGIN_REDIRECT_URI="/auth/login/google/callback"

#Facebook Auth Credential
FACEBOOK_CLIENT_ID=""
FACEBOOK_CLIENT_SECRET=""
FACEBOOK_REDIRECT_URI="/auth/facebook/callback"
FACEBOOK_LOGIN_REDIRECT_URI="/auth/login/facebook/callback"

#Google Analytics
GOOGLE_ANALYTICS_CREDENTIALS_URI="app/ga4_credential/ga4_service_account_dev.json"
GOOGLE_ANALYTICS_PROPERTY_ID="*********"

#Google maps and geocode
GOOGLE_GEOCODE_URL="https://maps.googleapis.com/maps/api/geocode/json"
GOOGLE_MAPS_API_KEY="AIzaSyBWDrBbwE1MaX-nUdzWm8vIYKynqa0XCgo"

FS_BASE_URL="https://dev.fs.pvgis.com" 
CITY_FILE_PATH="data/world.cities.xlsx"

X_APP_KEY="dWxs!LCJw!c2V1ZG8iOiJ0aXRpIiwiaWF0Ij+oxNzIz+TEwMTQ"
STRIPE_SECRETE_KEY='sk_test_51Psh8RCNxPSZy3wjvHlv1JZWQlTa1XiTPpwXtny3jzKq2Re2L3GPraje1JQKK68B9rhF038bJDtf4IlgGurA2oAQ00Uqs0Muk2'
STRIPE_ENDPOINT_SECRET='whsec_BrnISy57plz7GbR2sORrYZrTNBwXIwwP'
STRIPE_COUPON_FIFTY_ID='QljBFzrS'
APP_SECRET_KEY="3b6b0b24259f426167e8909d571afe1a5d4324392f619db730724ca76c6d9de6"
INVITATION_SECRET_KEY='gB9HlA1LjF9v3Eomq3Jd6GVwQU9bNsm5uLXZ-R9Bx2k='

SUPPORT_EMAIL='<EMAIL>'
WEBHOOK_ADMIN_EMAIL='<EMAIL>'
SMPT_USER_MAIL='<EMAIL>'
BILLING_EMAIL='<EMAIL>'
BILLING_EMAIL_PASSWORD='4Wm12hH88St7bPUZ'
MAIL_API='https://ms.pvgis.com'

IS_PROD=False


BACKEND_URL='http://localhost:8081'

CUSTOMER_EMAIL_SENDER_PASSWORD="{{Qwerty@123456}}"
CMS_URL="https://dev.cms.pvgis.com"
TRANSLATION_URL="https://dev.ts.pvgis.com"

DEFAULT_CAMPAIGN_MAIL_CHUNK_SIZE=400
DEFAULT_CAMPAIGN_MAIL_CHUNK_SIZE_PER_SENDER=150

STRIPE_DASHBOARD_BASE_URL="https://dashboard.stripe.com/test"

PRINT_URL= "https://print.pvgis.com/pvgis"
INSTALLERS_FILE_NAME="2025-06-05-installers.xlsx"


CUSTOMER_EMAIL_REPORT_RECIPIENT="<EMAIL>"

# CHATWOOT
CHATWOOT_WEBHOOK_SECRET_LINK="2969tuqngh2v6p17vm9y6f2l88qua4y3"
CHATWOOT_BASE_URL="https://dev.chatwoot.pvgis.com"
CHATWOOT_ACCOUNT_ID=1
CHATWOOT_API_ACCESS_TOKEN=XWRy7mNDaiKmeroDZeap8xtB