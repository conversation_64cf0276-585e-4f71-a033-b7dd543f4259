"""add_chatwoot_keys

Revision ID: 8c1fb713c767
Revises: b2b0dbc0fe2a
Create Date: 2025-08-18 10:42:26.481742

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '8c1fb713c767'
down_revision: Union[str, None] = 'b2b0dbc0fe2a'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('contact_platform', sa.Column('chatwoot_key', sa.String(length=255), nullable=True))
    op.execute("update contact_platform set chatwoot_key = 'facebook' where `key` = 'fb'")
    op.execute("update contact_platform set chatwoot_key = 'linkedin' where `key` = 'linkedin'")
    op.execute("update contact_platform set chatwoot_key = 'twitter' where `key` = 'x-tweet'")
    op.execute("update contact_platform set chatwoot_key = 'instagram' where `key` = 'instagram'")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
     
    op.drop_column('contact_platform', 'chatwoot_key')
    # ### end Alembic commands ###
