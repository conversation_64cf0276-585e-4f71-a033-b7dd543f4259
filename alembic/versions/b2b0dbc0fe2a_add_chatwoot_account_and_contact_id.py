"""add chatwoot account and contact id

Revision ID: b2b0dbc0fe2a
Revises: 20c5a5d87aa3
Create Date: 2025-08-14 08:35:47.733985

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'b2b0dbc0fe2a'
down_revision: Union[str, None] = '20c5a5d87aa3'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    
    op.add_column('customer', sa.Column('chatwoot_contact_id', sa.Integer(), nullable=True))
    op.add_column('customer', sa.Column('chatwoot_account_id', sa.Integer(), nullable=True))
   
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
 
    op.drop_column('customer', 'chatwoot_account_id')
    op.drop_column('customer', 'chatwoot_contact_id')
     
    # ### end Alembic commands ###
