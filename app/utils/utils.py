from collections.abc import Iterable
import requests
from app.config.settings import get_settings
from fastapi.encoders import jsonable_encoder
from datetime import datetime
from enum import Enum
from typing import Dict, Any
from decimal import Decimal
from datetime import datetime

settings = get_settings()

def to_dict(obj):
    return {c.key: getattr(obj, c.key) for c in obj.__table__.columns}

def send_email(email_param, lang, template):
    email_param["templateVars"]["current_year"] = datetime.now().year
    url = f"{settings.MAIL_API}/send/{lang}/{template}"

    response = requests.post(url, json=email_param)
    if response.status_code == 200:
        return response.json()
    else:
        print(f"Error: {response.status_code}")
        return None
    
def send_email_cms(email_param, lang, cmsKey):
    email_param["templateVars"]["current_year"] = datetime.now().year
    url = f"{settings.MAIL_API}/send/cms/{lang}/{cmsKey}" 
    response = requests.post(url, json=email_param) 
    if response.status_code == 200:
        return response.json()
    else:
        print(f"Error: {response.status_code}")
        print(response.text)
        return None
     
def custom_jsonable_encoder(obj):
    try:
        data = jsonable_encoder(obj)
    except:
        data = to_dict(obj)
    return data

def get_from_emb_dict_or_throw(emb_dict_arr, *args): 
    (result, key_not_found, source_dict_arr) = get_from_emb_dict_core(emb_dict_arr, *args)
    if key_not_found:
        raise Exception(f'No key/index "{key_not_found}" in dict/indexable \n'+str(source_dict_arr))
    return result

def get_from_emb_dict_core(emb_dict_arr, *args): 
     
    cur_value = emb_dict_arr
    for key in args:
        if cur_value is None:
            return (None, key, None)
        if (
            (
                isinstance(key, int) and is_indexable(cur_value)
            ) 
            or key in cur_value
        ):
            cur_value = cur_value[key] 
        else:
            return (None, key, cur_value)
    return (cur_value, None, None)

def get_from_emb_dict (emb_dict, *args):
    (result, key_not_found, source_dict_arr) = get_from_emb_dict_core(emb_dict, *args)
    return result

def is_indexable(obj):
    try:
        obj[0]  # Check if indexing works
        return isinstance(obj, Iterable)  # Ensure it's an iterable
    except (TypeError, IndexError, KeyError):
        return False
    
def serialize_value(value):
    if isinstance(value, datetime):
        return value.isoformat()
    elif isinstance(value, Enum):
        return value.value
    elif isinstance(value, Decimal):
        return float(value)  # Convert Decimal to float for JSON serialization
    elif isinstance(value, (list, dict)):
        return value  # let json.dumps handle nested structures
    else:
        return value

def serialize_dict_to_json(data: Dict[str, Any]) -> Dict[str, Any]:
    def serialize_recursive(value):
        if isinstance(value, dict):
            return {k: serialize_recursive(v) for k, v in value.items()}
        elif isinstance(value, list):
            return [serialize_recursive(item) for item in value]
        else:
            return serialize_value(value)
    
    return serialize_recursive(data)