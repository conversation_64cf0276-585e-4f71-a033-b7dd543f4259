from datetime import datetime, timedelta
from typing import List
from app import crud 
from app.api.api_v2.endpoints import customer_email
from app.config.settings import get_settings
from app.crud import crud_customer_email_item
from app.events import customer
from app.models.customer_email_item import CustomerEmailItem
from app.models.customer_email_task import CustomerEmailTask
from app.schemas.customer_email_item import CustomerEmailItemGenerateParams
from app.services.customer_email.customer_email_item_factories import generate_email_items
from app.services.stripe.stripe_service import get_welcome_discount_validity_period
from app.utils.utils import send_email
from app.utils.utils import send_email_cms
from sqlalchemy.orm import Session
from sqlalchemy import Date, cast, func, text
import traceback
import json
import pandas as pd
import urllib.parse
settings = get_settings()


 
def execute_customer_email_campaign(db: Session, campaign_key: str, params: CustomerEmailItemGenerateParams):
    email_tasks = crud.customer_email_task.load_all(db)
    email_tasks = [task for task in email_tasks if task.customer_email_campaign.key == campaign_key]
    
    params.setup_chunk_size(len(email_tasks))
    
    email_items = []
    
    for task in email_tasks:
        cur_email_items = task.generate_customer_email_items(self=task, db=db, params=params)
        email_items.extend(cur_email_items)
    
    return send_email_items(db, email_items)

    
def execute_customer_email_tasks(db: Session, 
                                 params: CustomerEmailItemGenerateParams, 
                                 task_key = None,
                                 task_keys: List = None,
                                 ):
    
    if not task_key and not task_keys:
        raise ValueError("Either task_key or task_keys must be provided.")
    
    email_tasks = crud.customer_email_task.load_all(db)
    if task_key:
        email_tasks = [task for task in email_tasks if task.key == task_key]
    if task_keys:
        email_tasks = [task for task in email_tasks if task.key in task_keys]
    params.setup_chunk_size(len(email_tasks))
    email_items = []
    for task in email_tasks:
        cur_email_items = task.generate_customer_email_items(self=task, db=db, params=params)
        email_items.extend(cur_email_items)
    
    return send_email_items(db, email_items)
             



def send_email_items(db: Session, email_items: List[CustomerEmailItem]):
    email_sent_count_per_sender = {}
    email_items_sent = []
    
    for email_item in email_items: 
        if not count_email_sent_by_sender(email_sent_count_per_sender, email_item):
            break
         
        send_email_item(db, email_item)
        email_items_sent.append(email_item)
        
    return email_items_sent
# Counts the email sent by sender and returns false if cant send email anymore 
def count_email_sent_by_sender(email_sent_count_per_sender, email_item):
    current_count = email_sent_count_per_sender.get(email_item.sender_email, 0)
    if current_count >= int(settings.DEFAULT_CAMPAIGN_MAIL_CHUNK_SIZE_PER_SENDER):
        return False
    email_sent_count_per_sender[email_item.sender_email] = current_count + 1
    return True
    

def send_email_item(db, email_item: CustomerEmailItem, throw_error = False):
    try:
        email_results = None 
        email_error = None
        try:
            email_results = send_customer_email_item(db, email_item)
            if not email_results :
                raise ValueError(f"Failed to send email for Customer with ID {email_item.customer.id}")
        except Exception as e: 
            if throw_error: 
                raise e
            email_error = traceback.format_exc()
            traceback.print_exc()
            
            
        if email_results:
            email_item.sent_at = email_item.generation_params.current_datetime
        else:
            email_item.error = email_error
        create_or_update_email_item (db, email_item) 
        # TODO: Eager load the relations ( DONT REMOVE FOR NOW (loading lazy relations))
        country = email_item.country
        if country:
            country_name = country.name
        customer_id = email_item.customer.id
        #END DONT REMOVE
        
        if email_results:
            send_customer_email_item_admin_confirmation(email_item) 
    except Exception as e: 
        traceback.print_exc()
        
def create_or_update_email_item(db, email_item: CustomerEmailItem):
    existing_email_item = next(iter(db.query(CustomerEmailItem).filter(
        CustomerEmailItem.customer_id == email_item.customer_id,
        CustomerEmailItem.customer_email_task_id == email_item.customer_email_task_id
    ).all()), None)
    if existing_email_item:
         
        crud.customer_email_item.update(db=db,db_obj=existing_email_item,  obj_in={
            "sent_at": email_item.sent_at,
            "sender_email": email_item.sender_email,
            "language": email_item.language,
            "country_id": email_item.country_id,
            "error": email_item.error,
        })
        
    else:
        crud.customer_email_item.create(db=db, obj_in=  email_item) 
    
    
def send_customer_email_item(db: Session, email_item: CustomerEmailItem):
  
    if not email_item.customer_email_task:
        raise ValueError(f"CustomerEmailTask for CustomerEmailItem with ID {email_item.id} is not loaded.")
    
    # Pre send process
    customer_email_task: CustomerEmailTask = email_item.customer_email_task
    if customer_email_task.pre_sending_process:
        customer_email_task.pre_sending_process(self=customer_email_task, item=email_item, db=db)
    
    
    cms_key = email_item.customer_email_task.cms_key
    customer = email_item.customer  

    if customer is None:
        raise ValueError("Customer associated with the email item is not found.")
    lang = email_item.language if email_item.language else "en"
    
    last_offer_day = email_item.get_last_offer_day()
    testimony_customer_data = email_item.get_testimony_customer_data()
    testimony_customer_data = {} if not testimony_customer_data else testimony_customer_data
    
    email_param = {
        "templateVars": {
            "firstname": customer.first_name,
            "lastname": customer.last_name,
            "email_sender": email_item.sender.name,  
            "number_mail" :email_item.customer_email_task.email_number, 
            "country": email_item.country.name if email_item.country else 'UNKNOWN',
            "language":lang,
            "pvgis_ui_url": settings.PVGIS_UI_URL+ f'/{lang}',
            "unsubscribeUrl": settings.PVGIS_UI_URL + '/customer/unsubscribe',
            "pvgis24ExtranetUrl": settings.PVGIS_UI_URL + f'/pvgis24/app/{customer.id}/pvgis24',
            "pvgis24SubscriptionUrl": settings.PVGIS_UI_URL + f'/{lang}/subscription',
            "pvgis24PremiumSubscriptionUrl": settings.PVGIS_UI_URL + '/subscribe/premium',
            "subscribeUrl": settings.PVGIS_UI_URL + f'/{lang}/subscription',
            "pvgis24Url": settings.PVGIS_UI_URL + f'/pvgis24/app/{customer.id}/pvgis24',
            "homePageUrl": settings.PVGIS_UI_URL + f'/{lang}',
            "testimony_user_name":  testimony_customer_data.get("first_name", "John") ,
            "testomony_link":settings.PVGIS_UI_URL+ f'/{lang}/subscription',
            "pvgis_offer_link": settings.PVGIS_UI_URL+ f'/{lang}/subscription?requires_login=1', 
            "last_offer_day":  last_offer_day.strftime('%d/%m/%Y') if last_offer_day else None,
            "half_price_month_count":  int(email_item.generation_params.special_offer_duration.days/30),
            **(email_item.additional_template_vars or {})
        },
        "mailVars": {
            "from": email_item.sender.email,
            "to": customer.email, 
            "force_from":True,
            "user_password":settings.CUSTOMER_EMAIL_SENDER_PASSWORD
        },
        "baseUrls": {"cmsUrl": settings.CMS_URL, "translationUrl": settings.TRANSLATION_URL},
        "sentByApp": "app",
        "sentToUser": "bla",
        "sentByProcess": "Customer", 
        "type": "MAIL", 
    }
    
    
    return send_email_cms(email_param, lang, cms_key)



def send_customer_email_item_admin_confirmation(email_item: CustomerEmailItem,isAdmin=True):
    

    if not email_item.customer_email_task:
        raise ValueError(f"CustomerEmailTask for CustomerEmailItem with ID {email_item.id} is not loaded.")
    cms_key = email_item.customer_email_task.cms_key
    customer = email_item.customer  

    if customer is None:
        raise ValueError("Customer associated with the email item is not found.")
    lang = email_item.language if email_item.language else "en"
     
    last_offer_day = email_item.get_last_offer_day()
    testimony_customer_data = email_item.get_testimony_customer_data()
    testimony_customer_data = {} if not testimony_customer_data else testimony_customer_data
    account_info = customer.account_information
    displayed_name = " ".join(filter(None, [customer.first_name, customer.last_name]))
    if account_info and account_info.company_name:
        displayed_name = account_info.company_name
        
    customer_email_campaign = email_item.customer_email_task.customer_email_campaign
    customer_email_campaign_name = None
    if customer_email_campaign:
        customer_email_campaign_name = customer_email_campaign.name  
    
    
    email_param = {
        "templateVars": {
            "email_task_name": email_item.customer_email_task.name, 
            "displayed_name": displayed_name,
            "campaign_name": customer_email_campaign_name or '-',
            "firstname": customer.first_name,
            "lastname": customer.last_name,
            "email_sender": email_item.sender.name,  
            "recipient_email": customer.email,    
            "number_mail" :email_item.customer_email_task.email_number, 
            "country": email_item.country.name if email_item.country else 'UNKNOWN',
            "language":lang, 
            "mailing_list_removal_url": settings.PVGIS_UI_URL + '/users/remove-from-mailing-list?email='+ customer.email,
            "pvgis_ui_url": settings.PVGIS_UI_URL+ f'/{lang}',
            "unsubscribeUrl": settings.PVGIS_UI_URL + '/customer/unsubscribe',
            "pvgis24ExtranetUrl": settings.PVGIS_UI_URL + f'/pvgis24/app/{customer.id}/pvgis24',
            "pvgis24SubscriptionUrl": settings.PVGIS_UI_URL + f'/{lang}/subscription',
            "pvgis24PremiumSubscriptionUrl": settings.PVGIS_UI_URL + '/subscribe/premium',
            "subscribeUrl": settings.PVGIS_UI_URL + f'/{lang}/subscription',
            "pvgis24Url": settings.PVGIS_UI_URL + f'/pvgis24/app/{customer.id}/pvgis24',
            "homePageUrl": settings.PVGIS_UI_URL + f'/{lang}',
            "testimony_user_name":  testimony_customer_data.get("first_name", "John") ,
            "testomony_link":settings.PVGIS_UI_URL+ f'/{lang}/subscription',  
            "pvgis_offer_link": settings.PVGIS_UI_URL+ f'/{lang}/subscription?requires_login=1', 
            "last_offer_day":  last_offer_day.strftime('%d/%m/%Y') if last_offer_day else None,
            "half_price_month_count":  int(email_item.generation_params.special_offer_duration.days/30),
            
            #"verification_url": "http://bla.com", 
            # "last_day": "31/01/25",
            # "otherCustomer": "blo", 
        },
        "mailVars": {
            "from": email_item.sender.email,
            "to": settings.CUSTOMER_EMAIL_REPORT_RECIPIENT, 
            "force_from":True,
            "user_password":settings.CUSTOMER_EMAIL_SENDER_PASSWORD
        },
        "baseUrls": {"cmsUrl": settings.CMS_URL, "translationUrl": settings.TRANSLATION_URL},
        "sentByApp": "app",
        "sentByProcess": "Customer", 
        "type": "MAIL", 
        "sentToUser": "bla",
        "isAdmin" : isAdmin,
        "cmskeyAdmin":"pvgis-admin-confirmation-email" 
    }
    
    return send_email_cms(email_param, lang, cms_key)
 
def is_customer_eligible_for_extended_promotion(db: Session, customer_id, current_datetime):
    availability_duration = CustomerEmailItem.EXTENDED_PROMOTION_AVAILABILITY_DURATION_DAYS
    query= (db.query(
        CustomerEmailItem
    )
    .join(CustomerEmailTask, CustomerEmailItem.customer_email_task_id == CustomerEmailTask.id)
    .filter(
        CustomerEmailItem.customer_id == customer_id, 
        CustomerEmailTask.key == 'special_offer',
        CustomerEmailItem.sent_at.isnot(None),  # Only consider sent emails
        cast (
            func.date_add(CustomerEmailItem.sent_at, text(f'INTERVAL {availability_duration} DAY')), Date
            ) >= cast(current_datetime, Date)
    ))
    # print( str(query.statement.compile(compile_kwargs={"literal_binds": True})).replace('\n', ''))
    customer_email_items = query.all()
    
    return len(customer_email_items) != 0 
         
    

def send_daily_report(db: Session, current_datetime): 
    sent_email_stats = crud.customer_email_item.get_customer_email_item_stats(db, current_datetime)
    
    if isinstance(sent_email_stats.get('stats_date'), datetime):
    # Convertir 'stats_date' en chaîne ISO 8601
        sent_email_stats['stats_date'] = sent_email_stats['stats_date'].strftime('%d/%m/%Y')
    cms_key ="pvgis-daily-report"
  

   

    sent_email_stats['attachments'] = [ 
                {
                    "filename": f"customer_email_report_{current_datetime}.xlsx",
                    "path": f"{settings.BACKEND_URL}/customer-emails/excel-report?current_datetime={ urllib.parse.quote(str(current_datetime), safe=':/?&=')}&x_app_key={urllib.parse.quote(settings.X_APP_KEY, safe=':/?&=')}",
                    
                },
            ] 
   
   
    email_param = {
        "templateVars":sent_email_stats,
        "mailVars": {
            "from": "<EMAIL>",
            "to": settings.CUSTOMER_EMAIL_REPORT_RECIPIENT, 
            "force_from":True,
            "user_password":settings.CUSTOMER_EMAIL_SENDER_PASSWORD
        },
        "baseUrls": {"cmsUrl": settings.CMS_URL, "translationUrl": settings.TRANSLATION_URL},
        "sentByApp": "app",
        "sentByProcess": "Daily report",
        "sentToUser": "Info User",
        "type": "MAIL",
    }
    lang = "en" 
    send_email_cms(email_param, lang, cms_key)
    
    
def generate_daily_report_dataframe(db, current_datetime):
    customer_email_items = crud.customer_email_item.get_daily_report(db, current_datetime)
     
    excel_rows = [
        {
            "marketing_email_number": item.customer_email_task.email_number,
            "sender_email": item.sender_email, 
            "language": item.language,
            "user_email": item.customer.email,
            "user_country":  item.country.name if item.country else 'UNKNOWN',
            "sent_at": item.sent_at, 
        } 
        for item in customer_email_items
    ]

     
    df = pd.DataFrame(excel_rows)

   
    column_mapping = {
        "marketing_email_number": "Marketing email number",
        "sender_email": "Sender Email",
        "language": "Language",
        "user_email": "User Email",
        "user_country": "User Country",
        "sent_at": "Sent at"
    }
    df = df.rename(columns=column_mapping)
    return df


def execute_email_task_for_customer(db: Session, customer_id: str, task_key:str, current_datetime):
    """
    FOR TESTING PURPOSES 
    """
    email_tasks = crud.customer_email_task.load_all(db)
    email_task = next((task for task in email_tasks if task.key == task_key), None)
    if not email_task:
        return None
    customer_countries = crud.customer_email_item.get_customer_data_for_mail(db,customer_id, current_datetime)
    mock_params = CustomerEmailItemGenerateParams(
        current_datetime = current_datetime, 
        account_type_id=2,
        special_offer_duration=timedelta(days=1*30)  # 30 days discount for welcome emails.
    )
    
    
    email_items = generate_email_items(customer_countries,email_task,mock_params)
    for email_item in email_items: 
        #pass
        send_email_item(db, email_item)
    return email_items