from app.models.customer import Customer
import requests
from app.config.settings import get_settings
from typing import Dict, Any
from fastapi import HTT<PERSON>Exception
from fastapi.encoders import jsonable_encoder
import json
from app import crud
from app.utils.utils import get_from_emb_dict
settings = get_settings()

def update_contact(
        db,
        chatwoot_contact_id: int, 
        chatwoot_account_id: int, 
        customer: Customer, 
        social_profiles: Dict[str, Any], 
        ):
    additional_attributes = {
        "city": customer.city,
        "social_profiles": social_profiles,
    }
    
    account_information = crud.account_information.get_first_where_array(
            db=db,
            where=[
                {"key": "customer_id", "operator": "==", "value": customer.id}
            ]
        )
    if account_information:
        additional_attributes["company_name"] = account_information.company_name
    
    country = crud.country.get(db=db, id=customer.country_id)
    if country :
        additional_attributes["country"] = country.name
        additional_attributes["country_code"] = country.code_alpha_2
        
    url = f"{settings.CHATWOOT_BASE_URL}/api/v1/accounts/{chatwoot_account_id}/contacts/{chatwoot_contact_id}"
    payload: Dict[str, Any] = {
        "additional_attributes": additional_attributes,
        "name": customer.full_name,
        "email": customer.email,
        "phone_number": customer.mobile_number,
        "identifier": str(customer.id),
        "avatar_url": get_from_emb_dict(customer.profile_image_json, "thumbnail_180")
    }
    headers = {
        "api_access_token": settings.CHATWOOT_API_ACCESS_TOKEN,
        "Content-Type": "application/json"
    }
    print(url)
    print(json.dumps(payload))
    print(json.dumps(headers))
    response = requests.put(url, json=payload, headers=headers) 
    
    if 200 <= response.status_code < 300:
        return True
    else: 
        print(response.text)
        raise HTTPException(
            status_code=500, 
            detail=f"Error when updating chatwoot contact"
        )
     
 
    """
    Chatwoot additional information
    {"city": "", "country": "Afghanistan", "description": "", "company_name": "", "country_code": "AF", "social_profiles": {"facebook": "fff"}}
    
    """
    
    
def update_customer_from_contact(
        db,
        customer_id: int, 
        contact_data: Dict[str, Any]
):
    # init update data
    customer = crud.customer.get_first_where_array(
        db=db,
        where=[
            {"key": "id", "operator": "==", "value": customer_id}
        ]
    ) 
    if not customer:
        return False
    country = None
    if customer.country_id:
        country = crud.country.get(db=db, id=customer.country_id)
    account_info = crud.account_information.get_first_where_array(
                    db=db,
                    where=[{"key": "customer_id", "operator": "==", "value": customer.id}]
                )
    
    
    customer_update = {}
    account_info_update = {}
    
    social_profiles_update = {}
    for changed_attribute in contact_data["changed_attributes"]:
        
        chatwoot_key = list(changed_attribute.keys())[0]
        if chatwoot_key == "phone_number":
            customer_update['mobile_number'] = changed_attribute[chatwoot_key]['current_value']
            
        elif chatwoot_key == "additional_attributes":
            changed_additional_attributes = changed_attribute[chatwoot_key]
            previous_aa_value = changed_additional_attributes['previous_value']
            new_aa_value = changed_additional_attributes['current_value']
            
            if has_additional_attributes_changed(previous_aa_value, new_aa_value, "city"):
                customer_update['city'] = new_aa_value['city']
            
            if has_additional_attributes_changed(previous_aa_value, new_aa_value, "country_code"):
                country = crud.country.get_first_where_array(
                    db=db,
                    where=[{"key": "code_alpha_2", "operator": "==", "value": new_aa_value['country_code']}]
                )
                if country:
                    customer_update['country'] = country.name
                    customer_update['country_id'] = country.id
            
            if has_additional_attributes_changed(previous_aa_value, new_aa_value, "company_name"):
                account_info_update['company_name'] = new_aa_value['company_name']
            
            # Social profiles
            previous_social_profiles = get_from_emb_dict(previous_aa_value, "social_profiles") or {}
            new_social_profiles = get_from_emb_dict(new_aa_value, "social_profiles") or {}
            
            for key in new_social_profiles.keys():
                if previous_social_profiles.get(key) != new_social_profiles[key]:
                    social_profiles_update[key] = new_social_profiles[key]

    
    # Create or Updates
    print('account_info_update----------------------------------')
    print(jsonable_encoder(account_info))
    print(account_info_update)
    if len(customer_update.keys()) > 0:
        crud.customer.update(db=db, db_obj=customer, obj_in=customer_update)
    if len(account_info_update.keys()) > 0 and account_info:
        print("UPDATING ACCOUNT INFO-----------------------------")
        crud.account_information.update(db=db, db_obj=account_info, obj_in=account_info_update)
    if len(social_profiles_update.keys()) > 0:
        for chatwoot_key, value in social_profiles_update.items():
            contact_platform = crud.contact_platform.get_first_where_array(
                db=db,
                where=[{"key": "chatwoot_key", "operator": "==", "value": chatwoot_key}]
            )
            if not contact_platform:
                continue
            crud.customer_contact_platform_information.update_or_create(
                db=db,
                customer_id=customer.id,
                contact_platform_id= contact_platform.id,
                value=value
            )
    
    
    
    return True


def restore_chatwoot_contact_forbidden_fields(customer: Customer, event: Dict[str, Any], chatwoot_account_id: int, chatwoot_contact_id: int):
    forbidden_chatwoot_keys_with_original_value = {
        "email":  customer.email,
        "name":  customer.full_name
    }
    restore_update = {}
    for changed_attribute in event["changed_attributes"]:
        chatwoot_key = list(changed_attribute.keys())[0]
        original_value = forbidden_chatwoot_keys_with_original_value.get(chatwoot_key)
        if (
            chatwoot_key in forbidden_chatwoot_keys_with_original_value 
            and changed_attribute[chatwoot_key]['current_value'] 
                != original_value
            ):
            restore_update[chatwoot_key] = original_value
     
    if len(restore_update.keys()) > 0:    
        url = f"{settings.CHATWOOT_BASE_URL}/api/v1/accounts/{chatwoot_account_id}/contacts/{chatwoot_contact_id}"
        
        headers = {
            "api_access_token": settings.CHATWOOT_API_ACCESS_TOKEN,
            "Content-Type": "application/json"
        }
        
        response = requests.put(url, json=restore_update, headers=headers) 
        
        if 200 <= response.status_code < 300:
            return True
        else: 
            print(response.text)
            raise HTTPException(
                status_code=500, 
                detail=f"Error when updating chatwoot contact"
            )
         
    return False
    
    
def has_additional_attributes_changed(previous_value, current_value, key) -> bool:
    prev_key_value = get_from_emb_dict(previous_value, key)
    new_key_value = get_from_emb_dict(current_value, key)
    return prev_key_value != new_key_value