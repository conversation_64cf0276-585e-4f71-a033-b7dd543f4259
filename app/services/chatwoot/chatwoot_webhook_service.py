from typing import Any, Dict, Optional
from app.utils.utils import get_from_emb_dict

 




def extract_contact_id(payload: Dict[str, Any]) -> Optional[int]: 
    candidates = [
        get_from_emb_dict(payload, "contact_inbox", "contact_id"),
        get_from_emb_dict(payload, "contact", "id"),
        get_from_emb_dict(payload, "conversation", "contact_id"),
        get_from_emb_dict(payload, "conversation", "meta", "sender", "id"),
    ]
    for c in candidates:
        if isinstance(c, int):
            return c
        # sometimes string
        if isinstance(c, str) and c.isdigit():
            return int(c)
    return None


def extract_pvgis_token(payload: Dict[str, Any]) -> Optional[str]:
    # Search common places
    candidates = [
        get_from_emb_dict(payload, "custom_attributes", "pvgis_user_token"),
        get_from_emb_dict(payload, "conversation", "custom_attributes", "pvgis_user_token"),
        get_from_emb_dict(payload, "conversation", "custom_attributes", "pvgis_user_token"),
        get_from_emb_dict(payload, "contact", "custom_attributes", "pvgis_user_token"),
        get_from_emb_dict(payload, "contact", "custom_attributes", "pvgis_user_token"),
        get_from_emb_dict(payload, "meta", "sender", "custom_attributes", "pvgis_user_token"),
    ]
    for c in candidates:
        if isinstance(c, str) and c:
            return c
    return None