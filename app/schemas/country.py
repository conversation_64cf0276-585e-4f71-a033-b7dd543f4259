from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime
from .base import BaseSchema


# Shared properties for Country
class CountryBase(BaseSchema):
    name: str
    import_id: Optional[str] = None
    country_id: Optional[int] = None
    code_alpha_2: Optional[str] = None
    code_alpha_3: Optional[str] = None
    currencies: Optional[dict] = None
    languages: Optional[dict] = None
    status: Optional[bool] = None
    avg_residential_consumption_json: Optional[dict] = None
    use_avg_residential_consumption: Optional[bool] = None

class CountryCreate(CountryBase):
    pass

class CountryUpdate(CountryBase):
    name: Optional[str]
     

class CountryInDB(CountryBase):
    id: int

    class Config:
        orm_mode = True