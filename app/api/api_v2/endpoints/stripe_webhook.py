from typing import Any
from sqlalchemy.orm import Session
import traceback
from app.enums.receipt_status_enum import ReceiptStatusEnum
from app.enums.billing_period_interval_enum import BillingPeriodIntervalEnum
from app.enums.subscription_action_type_enum import SubscriptionActionTypeEnum
from app.models.customer import Customer
from app.models.stripe_temp_payment_success import StripeTempPaymentSuccess
from app.models.subscription import Subscription
from app.schemas.stripe_temp_payment_success import StripeTempPaymentSuccessCreate
from app.schemas.stripe_webhook import StripeWebhookCreate
from fastapi import APIRouter, Depends, HTTPException,Request, Header
from fastapi.responses import JSONResponse
from fastapi.encoders import jsonable_encoder 
from app.config.db.database import get_session
from app.utils.utils import send_email_cms, to_dict
from app.api.api_v2.deps import get_user
import requests
from dotenv import load_dotenv
import os
from app import schemas
from app import crud
from datetime import datetime,timedelta
from app.api.api_v2.deps import get_optional_customer
from typing import Optional
from app.enums.subscription_status_enum import SubscriptionStatusEnum
from app.services.notification.notification import send_notif
from app.config.settings import get_settings
from sqlalchemy import func, text
import stripe

from app.services.stripe import stripe_service
router = APIRouter()
import asyncio

settings = get_settings()
@router.get("")
def read_customers(
    db: Session = Depends(get_session),
    offset: int = 0,
    
) -> Any:
    return {"status":200}


@router.post("")
async def stripe_webhook(
    request: Request, 
    stripe_signature: str = Header(None),
    db: Session = Depends(get_session),
):
    endpoint_secret=settings.STRIPE_ENDPOINT_SECRET
    payload = await request.body()
    
    

    try:
        # Construct the event
        event = stripe.Webhook.construct_event(payload, stripe_signature, endpoint_secret)
    except ValueError:
        # Invalid payload
        raise HTTPException(status_code=400, detail="Invalid payload")
    except stripe.error.SignatureVerificationError:
        # Invalid signature
        raise HTTPException(status_code=400, detail="Invalid signature")
    
    try:
        event_id = event["id"]
        ignored_event_ids = [
            "evt_1RHKG2CNxPSZy3wjeGSmC289",
            "evt_3RHKFwCNxPSZy3wj0VgQP3ep",
        ] 
        event_type = event["type"]
    
        # check if event has already processed  
         
        check_and_save_webhook = True
        
         
        # check_and_save_webhook = False
        if check_and_save_webhook:
            saved_webhook = crud.stripe_webhook.get_first_where_array_v2(db=db, where=[{"key":"event_id", "operator":"==", "value": event_id}])
            if saved_webhook :
                print(f"This webhook has already been processed {event_type} {event_id}")
                return JSONResponse(status_code=200)

        
        # Handle the event
        if event_id in ignored_event_ids:
            pass
        elif event['type'] == 'checkout.session.completed':
             
            cart_reference = None
            customer_id = None
            token = None
            auto_renew = 0

            checkout_ob = event['data']['object']
            metadata = checkout_ob['metadata']
            cart_reference = metadata.get('cartReference')
            # print('WITH CART REF-----')
            # print(cart_reference)
            customer_id = metadata.get('customerId')
            # Check if cartReference is present in metadata
            coupon_end_date=None
            
            stripe_customer_id = checkout_ob['customer']
            
            
            # We must take the ongoing, to increase the right balance in case of additional credit purshased while having a future subscription planned
            active_subscription = crud.subscription.get_latest_active_sub(db, customer_id, 
                                                                          
                                                                           
                                                                          # is_ongoing=False
                                                                          )
            subscriptionActionType = SubscriptionActionTypeEnum(metadata['subscriptionActionType'])
            # Get  cart instance  
            cart=crud.cart.get_first_where_array(db=db,where=[{'key':'cart_reference',"value":cart_reference,"operator":"=="}])
            product_id = cart.product_id
            
            # Get product to buy
            product_to_buy = crud.product.get_product_details_or_throw(db=db, product_id=product_id)
            
            # if (
            #     subscriptionActionType == SubscriptionActionTypeEnum.DOWNGRADE
            #     and active_subscription
            #     and BillingPeriodIntervalEnum(active_subscription.product_json['billing_period_interval']) == BillingPeriodIntervalEnum.year
            #     and BillingPeriodIntervalEnum( product_to_buy.billing_period_interval) == BillingPeriodIntervalEnum.month
            #     ):
            #     raise Exception("Downgrade from yearly to monthly not allowed")
            
            subscription_stripe_id = checkout_ob.get('subscription', None)
            subscription_stripe_ob = stripe_service.get_latest_active_subscription(stripe_customer_id) if not subscription_stripe_id else stripe.Subscription.retrieve(subscription_stripe_id)
            subscription_stripe_id = subscription_stripe_ob['id'] if subscription_stripe_ob else None
            
            
            if SubscriptionActionTypeEnum.CREATION == subscriptionActionType :
                discount = subscription_stripe_ob.get("discount")
                if discount:
                    discount_end_date_timestamp = discount.get("end")
                    coupon_end_date = datetime.fromtimestamp(discount_end_date_timestamp)
                 
                 
               
            conditionBtwOffer = (metadata.get('Destination') and metadata.get('Init')) and (metadata.get('Destination') != metadata.get('Init')) 
            sceneBtwOffer = [metadata.get('Init') ,metadata.get('Destination')]

            # Handle successful payment logic 
            creation_res = crud.subscription.create_subscription_after_payment(db, product_to_buy,
                        active_subscription,checkout_ob,subscription_stripe_ob, customer_id,coupon_end_date, cart, conditionBtwOffer, sceneBtwOffer) 

            
            if conditionBtwOffer:
                if (sceneBtwOffer[0] == '1' and sceneBtwOffer[1] == '2'):
                    new_subscription_inst = creation_res['subscription']
                    # cancel prev sub in stripe 
                    stripe.Subscription.modify(subscription_stripe_ob['id'], cancel_at_period_end=True)
                    
                    # create new sub in stripe
                    new_subscription_stripe_ob = stripe_service.create_future_subscription(
                        stripe_customer_id, 
                        metadata,
                        new_subscription_inst)
                    
                    #save its stripe_id in the database
                    crud.subscription.update(db=db,db_obj=new_subscription_inst,obj_in={"subscription_stripe_id":new_subscription_stripe_ob['id']})
                if (sceneBtwOffer[0] == '2' and sceneBtwOffer[1] == '1'):
                    if subscriptionActionType == SubscriptionActionTypeEnum.DOWNGRADE or subscriptionActionType == SubscriptionActionTypeEnum.SWITCH:
                        stripe_service.update_stripe_subscription(db,
                                stripe_customer_id, 
                                cart_reference, 
                                subscription_stripe_ob,
                                None,
                                True
                        )
                    else:
                        stripe_service.update_stripe_subscription(db,
                                stripe_customer_id, 
                                cart_reference, 
                                subscription_stripe_ob,
                                None,
                                True, 
                                True
                        )
            else:
                if ( 
                    subscriptionActionType in [SubscriptionActionTypeEnum.UPGRADE, SubscriptionActionTypeEnum.SWITCH]
                    ):
                    stripe_service.update_stripe_subscription(db,
                            stripe_customer_id, 
                            cart_reference, 
                            subscription_stripe_ob,
                            
                    )
                    
                # Handle stripe actions (called only after db changes)
                elif SubscriptionActionTypeEnum.CREATION == subscriptionActionType : 
                    disable_auto_renew = metadata.get('disableAutoRenew')
                    if disable_auto_renew == '1': 
                        stripe.Subscription.modify(subscription_stripe_ob['id'], cancel_at_period_end=True)
                    
                
                elif SubscriptionActionTypeEnum.DOWNGRADE == subscriptionActionType   :
                    if ('refundAmount' in metadata and  float(metadata['refundAmount']) > 0) :
                        
                        stripe_service.update_stripe_subscription(db,stripe_customer_id, cart_reference, subscription_stripe_ob)
                        
                        
                        refund_amount = float(metadata['refundAmount'])
                        charges_data_to_refund = stripe_service.get_charges_to_refund(stripe_customer_id, refund_amount)
                        for cd in charges_data_to_refund:
                            ## DATABASE OPERATION
                            spt_invoice = creation_res['created_refund_invoice']
                            receipt = crud.subscription.save_receipt(db=db, 
                                    session_stripe_ob=checkout_ob,
                                    customer=creation_res['customer'],  
                                    subscription_stripe_id=subscription_stripe_ob['id'], 
                                    spt_invoice=spt_invoice,
                                    amount_already_paid = -float(cd['amount_already_refunded']),
                                    amount_paid = -float(cd['amount_to_refund']),
                                    remaining_to_pay = -float(cd['amount_to_refund_later']),
                                    status = ReceiptStatusEnum.PENDING
                                )
                            refund_stripe_ob = stripe.Refund.create(
                                amount=int(cd['amount_to_refund']*100),
                                charge=cd['charge']['id'],
                                metadata={
                                    "receiptId": receipt.id
                                }
                            )
                            
                            
                    
                    else:
                        new_subscription_inst = creation_res['subscription']
                        # cancel prev sub in stripe 
                        stripe.Subscription.modify(subscription_stripe_ob['id'], cancel_at_period_end=True)
                        
                        # create new sub in stripe
                        new_subscription_stripe_ob = stripe_service.create_future_subscription(
                            stripe_customer_id, 
                            metadata,
                            new_subscription_inst)
                        
                        #save its stripe_id in the database
                        crud.subscription.update(db=db,db_obj=new_subscription_inst,obj_in={"subscription_stripe_id":new_subscription_stripe_ob['id']})
                    
                    
               
            crud.subscription.send_notif_mail_after_payment(checkout_json=checkout_ob, customer = creation_res['customer'], vars = creation_res['notification_mail_vars'])
            
           
            

        elif event['type'] == 'payment_intent.succeeded':
            # For subscription changes, additional credit
            await asyncio.sleep(3) 
            payment_intent_ob = event['data']['object']
            metadata=payment_intent_ob["metadata"] 
          
            if  'cartReference' in  metadata: # no metadata cartRef when subscription_create or at subscription renewal
                subscription_stripe_ob = stripe_service.get_latest_active_subscription(payment_intent_ob['customer'])
                subscription_stripe_id = subscription_stripe_ob['id']
                subscription = crud.subscription.get_subscription_by_subscription_stripe_id(db, subscription_stripe_id)
                # some subscriptions might not hage the `subscription_stripe_id` column 
                if not subscription:
                    customer_id = metadata["customerId"]
                    subscription =  crud.subscription.get_latest_active_sub(db, customer_id) 
                cart_reference=metadata['cartReference']
                crud.subscription.save_receipt_or_renew(db,
                                                        cart_reference,
                                                        subscription,
                                                        payment_intent_ob,
                                                        subscription_stripe_id,
                                                        save_invoice=True
                                                        )
            
                
        elif event['type'] == 'invoice.payment_succeeded':
            # For subscription creation or automatic renewal
            await asyncio.sleep(3) 
            invoice_ob = event['data']['object']
             
            if invoice_ob['billing_reason'] in ['subscription_create', 'subscription_cycle']:
                subscription_stripe_id = invoice_ob['subscription'] 
                subscription = crud.subscription.get_subscription_by_subscription_stripe_id(db, subscription_stripe_id)
                
                # some subscriptions might not hage the `subscription_stripe_id` column 
                if not subscription and invoice_ob['billing_reason'] == 'subscription_cycle':
                    metadata = invoice_ob["lines"]["data"][0]["metadata"]
                    customer_id = metadata["customerId"]
                    subscription =  crud.subscription.get_latest_active_sub(db, customer_id)
                 
                metadata=invoice_ob["lines"]["data"][0]["metadata"]
                cart_reference = metadata['cartReference'] 
                crud.subscription.save_receipt_or_renew(db,cart_reference,subscription,invoice_ob,subscription_stripe_id,
                                                        save_invoice=False
                                                        )
                
            

        elif event['type'] == 'customer.subscription.created':
            subscription_stripe_ob = event['data']['object']
            # Handle subscription created event
        
        
        elif event['type'] == 'customer.subscription.updated':
            subscription_stripe_ob = event['data']['object']
            # Handle subscription updated event
        elif event['type'] == 'invoice.payment_failed':
            invoice = event['data']['object']        
            metadata=invoice["lines"]["data"][0]["metadata"]
            customer_id = int(metadata['customerId'])
            customer = crud.customer.get(db=db, id=customer_id)
            
            # Mark invoice as incollectible if no more attempts are scheduled
            if not(invoice['next_payment_attempt']):
                current_invoice = crud.subscription_payment_transaction.get_first_where_array_v2(db=db, 
                                             where=[{"key":"stripe_object_id", "operator":"==", "value": invoice['id']}])
                if current_invoice:
                    crud.subscription_payment_transaction.update(
                        db,
                        db_obj=current_invoice,
                        obj_in={"stripe_terminal_status": "uncollectible"},
                        commit=False,
                        flush=True
                    )
            
            
            
            #Send email to billing 
            send_failed_payment_billing_mail(event, customer)
            
            #Send email to customer after second attempt
            if invoice['billing_reason'] == 'subscription_cycle' and invoice['attempt_count'] == 2:
                
                crud.subscription.send_renewal_payment_failed_email(db=db,customer=customer)
            
            
            if invoice['billing_reason'] == 'subscription_create':
                # This indicates that the first payment attempt after trial ended failed
                subscription_id = invoice['subscription']
                customer_id = invoice['customer']
                
                # Handle the situation (e.g., notify customer, mark in system)
                # TODO: no more relevant, see handle_failed_payment definition
                # disable failed_payment for now
                # crud.subscription.handle_failed_payment(db=db,stripe_subscription_id=subscription_id, stripe_customer_id=customer_id)
                
        elif event['type'] == 'customer.subscription.trial_will_end':
            # Not relevant: this will send an email  when a subscription that has been marked as a trial will end, 
            # even if some trials are only meant to prevent payments upon update 
            # crud.subscription.handle_trial_will_end(db=db,event=event)
            pass
        elif event['type'] == "customer.subscription.deleted":
            data_ob = event["data"]["object"]
            crud.subscription.handle_subscription_end(db=db,data_ob=data_ob)
        elif event['type'] == "invoice.upcoming":
            data_ob = event["data"]["object"]
            if data_ob['billing_reason'] == 'subscription_cycle':
                crud.subscription.handle_before_renew(db=db,data_ob=data_ob)
        elif event['type'] == "invoice.created":
            invoice_ob = event["data"]["object"]
            if invoice_ob['billing_reason'] in ['subscription_create', 'subscription_cycle']:
                subscription_stripe_id = invoice_ob['subscription'] 
                subscription = crud.subscription.get_subscription_by_subscription_stripe_id(db, subscription_stripe_id)
                
                # some subscriptions might not hage the `subscription_stripe_id` column 
                if not subscription and invoice_ob['billing_reason'] == 'subscription_cycle':
                    metadata = invoice_ob["lines"]["data"][0]["metadata"]
                    customer_id = metadata["customerId"]
                    subscription =  crud.subscription.get_latest_active_sub(db, customer_id)
                    
                
                metadata=invoice_ob["lines"]["data"][0]["metadata"]
                cart_reference=metadata['cartReference']
                
                crud.subscription.save_invoice_if_not_exists(
                    db=db,
                    stripe_ob=invoice_ob,
                    cart_reference=cart_reference,
                    subscription_ob=subscription,
                    subscription_stripe_id=subscription_stripe_id
                )
        elif event['type'] == 'invoice.updated':
            terminal_statuses = ['paid', 'void', 'uncollectible']
            invoice_ob = event["data"]["object"]
            current_status = invoice_ob['status']
            current_invoice = crud.subscription_payment_transaction.get_first_where_array_v2(db=db, 
                                             where=[{"key":"stripe_object_id", "operator":"==", "value": invoice_ob['id']}])
            if current_invoice and current_status in terminal_statuses:
                crud.subscription_payment_transaction.update(
                    db,
                    db_obj=current_invoice,
                    obj_in={"stripe_terminal_status": current_status},
                    commit=False,
                    flush=True
                )
        elif event['type'] == "customer.deleted":
            # handle customer deleted
            stripe_customer_id = event["data"]["object"]["id"]
            crud.subscription.handle_stripe_customer_deleted(db=db,stripe_customer_id=stripe_customer_id)
        elif event["type"] == "refund.created" or event["type"] == "refund.updated":
            refund_ob = event["data"]["object"]
            
            if refund_ob['status'] == 'succeeded':
                metadata = refund_ob['metadata']
                receipt_id = metadata['receiptId']
                receipt = crud.subscription_payment_receipt.get(db=db, id=receipt_id)
                if receipt.status == ReceiptStatusEnum.PENDING:
                    crud.subscription_payment_receipt.update(
                        db=db,
                        db_obj=receipt,
                        obj_in={
                                "status": ReceiptStatusEnum.SUCCESS,
                                "stripe_reference": refund_ob['id']
                        }
                    )
                    db.commit() 
        else:
            event_type=event['type']
            # Unexpected event type
            print(f"Unhandled event type {event_type}")
        
        # save into database stripe_webhook
        stripe_in = StripeWebhookCreate(
            event_type=event_type,
            event_id=event_id,
        )
        if check_and_save_webhook:
            crud.stripe_webhook.create(db=db,obj_in=stripe_in, commit=False, flush=True)
        db.commit()
    except Exception as e:
        db.rollback()
        event_type=event['type']
        data = event["data"]
        error_details = {
            "type": type(e).__name__,
            "message": str(e),
            "traceback": traceback.format_exc(),
        }
        if settings.IS_PROD: 
            stripe_service.send_email_failed_webhook(webhook_event_type=event_type, webhook_data=data, error_details=error_details)
        raise

    return JSONResponse(status_code=200)

 
     
    
def send_failed_payment_billing_mail(event: dict, customer: Customer):
    cms_key = 'mail-billing-stripe-failed-payment'
    invoice = event['data']['object']
    currency_res = requests.get(f"{settings.SETTINGS_API_URL}/setting/key/pvgis__ui.default_currency")
    currency=currency_res.json()["value"]['symbol']
    paymentAmount = invoice['amount_due']/100
    paymentDate = datetime.fromtimestamp(invoice['created']).strftime('%d/%m/%Y') 
 
    stripeEventUrl = f"{settings.STRIPE_DASHBOARD_BASE_URL}/workbench/events?search={event['id']}"
    email_param = {
        "templateVars": {
            "customerId": customer.id,
            "currency": currency,
            "paymentAmount": paymentAmount, 
            "paymentDate": paymentDate,
            "stripeEventUrl": stripeEventUrl
        },
        "mailVars": {
            "to": settings.BILLING_EMAIL,
        },
        "baseUrls": {"cmsUrl": settings.CMS_URL, "translationUrl": settings.TRANSLATION_URL},
        "sentByApp": "app",
        "sentToUser": customer.first_name,
        "sentByProcess": "Pvgis", 
        "type": "MAIL", 
    }
    
    # print('EMAIL PARAMS')
    # print(email_param)
    return send_email_cms(email_param, 'en', cms_key)
 