from typing import Any
from app.models.customer import Customer
from fastapi import APIRouter, Depends, HTTPException, File, UploadFile
from sqlalchemy.orm import Session

from app.api.api_v2.deps import get_user, get_current_language
from app.config.db.database import get_session
from app.schemas.account_information import (
    AccountInformationCreate,
    AccountInformationUpdate,
)

from app.crud.crud_account_information import account_information
from app.crud.crud_customer import customer
from app.crud.crud_customer_contact_platform_information import customer_contact_platform_information
from app import schemas
from app import crud
from fastapi.encoders import jsonable_encoder
from typing import List
from app.services.notification.notification import send_notif
from app.services.chatwoot import chatwoot_service

router = APIRouter()

import ast


@router.get("")
def read_account_information(
        db: Session = Depends(get_session),
        offset: int = 0,
        limit: int = 100,
        where: str = "",
        order_by: str = "id",
        order: str = "ASC",
        relations: str = "['account_type', 'school_category', 'professional_category', 'customer']",
        base_columns: str = "['created_at']"
) -> Any:
    wheres = []
    base_columns = ast.literal_eval(base_columns)
    relations = ast.literal_eval(relations)
    if where is not None and where != "" and where != []:
        wheres += ast.literal_eval(where)
    count = crud.account_information.get_count_where_array(
        db=db,
        where=wheres,
    )
    account_information_list = crud.account_information.get_multi_where_array_v2(
        db=db,
        where=wheres,
        order_by=order_by,
        order=order,
        limit=limit,
        skip=offset,
        relations=relations,
        base_columns=base_columns,
    )

    return {
        "count": count,
        "data": account_information_list,
    }


@router.post("", response_model=Any)
def create_account_information(
        *,
        db: Session = Depends(get_session),
        account_information_in: schemas.AccountInformationCreate,
        current_user: Any = Depends(get_user),
) -> Any:
    """
    Create new account_information.
    """
    data = crud.account_information.create(db=db, obj_in=account_information_in)

    return data


@router.post("/save_full_information", response_model=Any)
def create_account_information(
        *,
        db: Session = Depends(get_session),
        full_information_in: schemas.FullAccountInformationCreate,
        current_user: Any = Depends(get_user),
        lang: str = Depends(get_current_language),
) -> Any:
    """
    Create new account_information.
    """
    reponse: Any = {}
    # UPDATE OR CREATE ACCOUNT INFORMATION
    wheres = [
        {
            "key": "customer_id",
            "operator": "==",
            "value": full_information_in.customer_id
        }
    ]
    account_information_data = crud.account_information.get_first_where_array(
        db=db,
        where=wheres
    )
    account_information_in = {
        "customer_id": full_information_in.customer_id,
        "account_type_id": full_information_in.account_type_id,
        "professional_category_id": full_information_in.professional_category_id,
        "school_category_id": full_information_in.school_category_id,
        "profession": full_information_in.profession,
        "other_category": full_information_in.other_category,
        "age_range": full_information_in.age_range,
        "company_name": full_information_in.company_name,
    }

    if (account_information_data):
        newAccountInfodata = crud.account_information.update(db=db, db_obj=account_information_data,
                                                             obj_in=account_information_in)
    else:
        newAccountInfodata = crud.account_information.create(db=db, obj_in=account_information_in)
    reponse['account_information'] = jsonable_encoder(newAccountInfodata),

    # UPDATE OR CREATE CUSTOMER
    customer_data = crud.customer.get(db=db, id=full_information_in.customer_id)
    customer_in = {
        "first_name": full_information_in.firstname,
        "last_name": full_information_in.lastname,
        "email": full_information_in.email,
        "street_address": full_information_in.rue,
        "district_postal_code": full_information_in.postal_code,
        "city": full_information_in.city,
        "country": full_information_in.country,
        "country_id": full_information_in.country_id,
        "pseudo": full_information_in.pseudo,
        "mobile_number": full_information_in.phone,
        "profile_image_json": full_information_in.profile_image_json,
        "company_logo_json": full_information_in.company_logo_json,
        "timezone_offset": full_information_in.timezone_offset,
    }
    # Check if pseudo or email already exists
    if (customer_in['pseudo']):
        existing_user_with_pseudo = db.query(Customer).filter((Customer.pseudo == customer_in['pseudo'])).first()
        if existing_user_with_pseudo: raise HTTPException(status_code=400, detail="pseudo_already_exist")
    if (customer_in['email']):
        existing_user_with_email = db.query(Customer).filter((Customer.email == customer_in['email'])).first()
        if existing_user_with_email: raise HTTPException(status_code=400, detail="email_already_exist")

    if (customer_data):
        customer_in = {k: v for k, v in customer_in.items() if v is not None}
        newCustomerData = crud.customer.update(db=db, db_obj=customer_data, obj_in=customer_in)
    else:
        newCustomerData = crud.customer.create(db=db, obj_in=customer_in)
    reponse['customer'] = jsonable_encoder(newCustomerData),

    # INSERT OR UPDATE CUSTOMER CONTACT PLATFORM INFORMATION
    chatwoot_social = {}
    reponse['social_media'] = []
    for social_media in full_information_in.social_medias:
        if (social_media['value']):
            wheres_cpi = [
                {
                    "key": "customer_id",
                    "operator": "==",
                    "value": full_information_in.customer_id
                },
                {
                    "key": "contact_platform_id",
                    "operator": "==",
                    "value": social_media['platform_id']
                }
            ]
            contact_platform_data = crud.customer_contact_platform_information.get_first_where_array(db=db,
                                                                                                     where=wheres_cpi,
                                                                                                    )
            contact_platform_in = {
                "customer_id": full_information_in.customer_id,
                "contact_platform_id": social_media['platform_id'],
                "value": social_media['value'],
            }
            if (contact_platform_data):
                new_contact_platform_data = crud.customer_contact_platform_information.update(db=db,
                                                                                       db_obj=contact_platform_data,
                                                                                       obj_in=contact_platform_in)
            else:
                new_contact_platform_data = crud.customer_contact_platform_information.create(db=db,
                                                                                       obj_in=contact_platform_in)
            reponse['social_media'].append(jsonable_encoder(new_contact_platform_data))
            
            contact_platform = crud.contact_platform.get(db=db, id=new_contact_platform_data.contact_platform_id)
            
            if (contact_platform.chatwoot_key):
                chatwoot_social[contact_platform.chatwoot_key] = new_contact_platform_data.value
    
    
    # Update chatwoot profile if any
    if(customer_data.chatwoot_contact_id and customer_data.chatwoot_account_id):
        chatwoot_service.update_contact(
            db,
            customer_data.chatwoot_contact_id, 
            customer_data.chatwoot_account_id, 
            newCustomerData, 
            chatwoot_social,
        )
     
    # send notification
    users = [
        {
            "id": current_user["id"],
            "roleId": 2,
            "appKey": "**********",
            "vars": {
            }
        }
    ]
    send_notif(event_code="account-info-updated", users=users, mailLanguage=lang)
    return reponse


@router.get("/{id}")
def read_account_information(
        *,
        db: Session = Depends(get_session),
        id: int,
        current_user: Any = Depends(get_user),
) -> Any:
    """
    Get account_information by ID.
    """
    data = crud.account_information.get(db=db, id=id)

    if not data:
        raise HTTPException(status_code=404, detail="AccountInformation not found")

    return data


@router.put("/support")
def update_account_information(
        *,
        db: Session = Depends(get_session),
        contact_information_id: int,
        account_information_in: schemas.AccountInformationUpdate,
) -> Any:
    """
    Update an account_information.
    """
    data = crud.account_information.get(db=db, id=contact_information_id)

    if not data:
        raise HTTPException(status_code=404, detail="AccountInformation not found")

    data = crud.account_information.update(db=db, db_obj=data, obj_in=account_information_in)

    return data


@router.put("/{id}")
def update_account_information(
        *,
        db: Session = Depends(get_session),
        id: int,
        account_information_in: schemas.AccountInformationUpdate,
        current_user: Any = Depends(get_user),
) -> Any:
    """
    Update an account_information.
    """
    data = crud.account_information.get(db=db, id=id)

    if not data:
        raise HTTPException(status_code=404, detail="AccountInformation not found")

    data = crud.account_information.update(db=db, db_obj=data, obj_in=account_information_in)

    return data



@router.delete("/{id}", response_model=Any)
def delete_account_information(
        *,
        db: Session = Depends(get_session),
        id: int,
        current_user: Any = Depends(get_user),
) -> Any:
    """
    Delete an account_information.
    """

    data = crud.account_information.get(db=db, id=id)

    if not data:
        raise HTTPException(status_code=404, detail="AccountInformation not found")

    return crud.account_information.soft_delete(db=db, id=id)


@router.delete("/facebook/delete_data", response_model=Any)
def update_account_information(
        *,
        db: Session = Depends(get_session),
        current_user: Any = Depends(get_user),
) -> Any:
    
    return "remove facebook data"
    # get facebook user data
    # URL de suppression de données Facebook pour un utilisateur
    # url = f"https://graph.facebook.com/v12.0/{user_id}/permissions"

    # # Paramètres requis par Facebook API
    # params = {
    #     'access_token': ACCESS_TOKEN
    # }

    # # Faire la requête de suppression
    # response = requests.delete(url, params=params)

    # # Gestion des erreurs et retour de la réponse
    # if response.status_code == 200:
    #     return {"message": "Les données utilisateur ont été supprimées avec succès de Facebook."}
    # else:
    #     raise HTTPException(status_code=response.status_code, detail=response.json())
