
from sqlalchemy.orm import Session
import traceback
from app.enums.receipt_status_enum import ReceiptStatusEnum
from app.enums.billing_period_interval_enum import BillingPeriodIntervalEnum
from app.enums.subscription_action_type_enum import SubscriptionActionTypeEnum
from app.models.customer import Customer
from app.models.stripe_temp_payment_success import StripeTempPaymentSuccess
from app.models.subscription import Subscription
from app.schemas.stripe_temp_payment_success import StripeTempPaymentSuccessCreate
from app.schemas.stripe_webhook import StripeWebhookCreate


from app.utils.utils import send_email_cms, to_dict
import requests

import os
from app import schemas

from datetime import datetime,timedelta
from app.api.api_v2.deps import get_optional_customer
from typing import Optional
from app.enums.subscription_status_enum import SubscriptionStatusEnum
from app.services.notification.notification import send_notif
from app.utils.utils import get_from_emb_dict
from sqlalchemy import func, text
import stripe


from dotenv import load_dotenv
from fastapi.responses import JSONResponse
from fastapi.encoders import jsonable_encoder 
from app.config.db.database import get_session

from app.api.api_v2.deps import get_user
from typing import Any, Dict, Optional
from fastapi import APIRouter, Depends, HTTPException,Request, Header
from app.services.stripe import stripe_service
from app.config.settings import get_settings
import asyncio
from app import crud
from app.services.chatwoot.chatwoot_webhook_service import extract_contact_id, extract_pvgis_token
from app.services.chatwoot import chatwoot_service

import json
router = APIRouter()

settings = get_settings()
 

@router.post(f'/{settings.CHATWOOT_WEBHOOK_SECRET_LINK}')
async def chatwoot_webhook(
    request: Request,  
    db: Session = Depends(get_session),
): 
    
    
    try:  
        event = await request.json()
         
        print('payload--------------------------')
        print(json.dumps(jsonable_encoder(event)))
        print('----------------------------------')
        if(event['event'] == 'contact_created' or event['event'] == 'contact_updated'):
            customer_id = event['identifier']
            if customer_id:
                customer_id = int(customer_id)
                chatwoot_contact_id = event['id']
                chatwoot_account_id = int(settings.CHATWOOT_ACCOUNT_ID)
                customer=crud.customer.get(db, customer_id)
                if customer and customer.chatwoot_contact_id is None:
                    crud.customer.update(db=db,db_obj=customer, obj_in={"chatwoot_contact_id": chatwoot_contact_id, "chatwoot_account_id": chatwoot_account_id, } )
        
                chatwoot_service.update_customer_from_contact(db, customer_id, event)
                chatwoot_service.restore_chatwoot_contact_forbidden_fields(customer, event, chatwoot_account_id, chatwoot_contact_id)
                
        return JSONResponse(content={"status": "success"}, status_code=200)
    except Exception as e:
        print('Error in chatwoot_webhook') 
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Error in chatwoot_webhook: {str(e)}")
   








"""
Example of contact created event
{
    "account": {
        "id": 2,
        "name": "PVGIS"
    },
    "additional_attributes": {},
    "avatar": "",
    "custom_attributes": {},
    "email": null,
    "id": 5,
    "identifier": null,
    "name": "fragrant-dew-17",
    "phone_number": null,
    "thumbnail": "",
    "blocked": false,
    "event": "contact_created"
}
"""