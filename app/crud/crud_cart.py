from fastapi.encoders import jsonable_encoder
from app.crud.base import CRUDBase
from app.models.cart import Cart
from sqlalchemy.orm import Session,joinedload, contains_eager

from app.models.product import Product 
from app.models.product_features import ProductFeatures
from app.schemas.cart import CartCreate,CartUpdate,DeleteCart
from app.crud.crud_customer import customer
from app import crud

class CRUDCart(CRUDBase[Cart, CartCreate,CartUpdate]):
    pass

    def remove_cart_by_reference(self, db: Session, cart_reference:str, cart_in:DeleteCart):
        existing_cart = self.get_first_where_array_v2(db=db, where=[
            {
                "key": "cart_reference",
                "operator": "==",
                "value": cart_reference
            },
           
        ] )
        if existing_cart and not existing_cart.converted_at:
            self.remove_where_array(
                db=db,
                where=[{"key": "cart_reference", "operator": "==", "value": cart_reference}],
                commit=True,
            )
        
        if(cart_in.customer_id):
            customer_db=crud.customer.get(db=db,id=cart_in.customer_id)
            crud.customer.update(db=db,db_obj=customer_db,obj_in={"cart_reference":None})
    
    def get_by_ref_for_checkout(self, db: Session, cart_ref: str):
        cart = self.get_first_where_array(
            db=db,
            where=[{"key": "cart_reference", "operator": "==", "value": cart_ref},{"key":"converted_at","operator":"isNull"}],
            relations=[
                "product.product_features.features",
                "subscription_payment_transaction", 
            ],
        )
        return cart
        


cart = CRUDCart(Cart)
