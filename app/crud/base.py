import ast
import json
from datetime import datetime, timedelta, date
from typing import Any, Dict, Generic, List, Literal, Optional, Type, TypeVar, Union
import re
import regex
from app.utils.utils import to_dict
from fastapi import HTTPException
from fastapi.encoders import jsonable_encoder
from pydantic import BaseModel
from sqlalchemy import and_, asc, delete, desc, extract, func, inspect, or_, case
from sqlalchemy.orm import (
    Session,
    joinedload,
    load_only,
)

from app.config import settings
from app.config.db.database import Base
from app.enums.subscription_status_enum import SubscriptionStatusEnum
from app.models.product import Product
from app.models.simulation import Simulation
from app.models.subscription import Subscription
from app.models.customer import Customer
from app import models
from sqlalchemy.ext.declarative import DeclarativeMeta


ModelType = TypeVar("ModelType", bound=Base)
CreateSchemaType = TypeVar("CreateSchemaType", bound=BaseModel)
UpdateSchemaType = TypeVar("UpdateSchemaType", bound=BaseModel)

def custom_jsonable_encoder(obj):
    try:
        data = jsonable_encoder(obj)
    except:
        data = to_dict(obj)
    return data

class CRUDBase(Generic[ModelType, CreateSchemaType, UpdateSchemaType]):
    def __init__(self, model: Type[ModelType]):
        """
        CRUD object with default methods to Create, Read, Update, Delete (CRUD).

        **Parameters**

        * `model`: A SQLAlchemy model class
        * `schema`: A Pydantic model (schema) class
        """
        self.model = model

    def get(
        self,
        db: Session,
        id: Any,
        where: Any = "",
        relations=None,
        current_user=None,
        include_deleted=False,
    ) -> Optional[ModelType]:
        query = db.query(self.model).select_from(self.model).filter(self.model.id == id)

        if where is not None and where != "":
            where = ast.literal_eval(where)
            conditions = self.get_full_condition(
                db=db,
                where=where,
                current_user=current_user,
                include_deleted=include_deleted,
            )
            if conditions is not None:
                query = query.filter(conditions)

        if relations is not None and len(relations) > 0:
            query = query.options(*(self.get_all_relations(relations)))

        return query.first()

    def get_permission(self, get_permission_):
        if get_permission_ == 0:
            raise HTTPException(status_code=405, detail="Routes not found")
        if not get_permission_:
            pass
            # raise HTTPException(status_code=405, detail="Routes Permission not found")
        if get_permission_ == 2:
            raise HTTPException(status_code=405, detail="Not enough permissions")

    def get_all_relations(self, relations: List):
        response = [self.get_joined_load(x) for x in relations]
        return response

    def get_joined_load(self, column_name):
        parts = column_name.split(".")
        previous_model = self.model
        result = None
        for i in range(0, len(parts)):
            attr = getattr(previous_model, parts[i])
            if result:
                result = result.joinedload(attr)
            else:
                result = joinedload(attr)
            if i < len(parts) - 1:
                previous_model = attr.property.mapper.class_
        return result

    def get_joined_load_v2(self, relations):
        def process_relation(relation):
            parts = relation.split(".")
            previous_model = self.model
            result = None

            for i in range(len(parts)):
                part = parts[i]

                if "{" in part and "}" in part:
                    relationship, columns = part.split("{")
                    columns = columns.rstrip("}").split(",")
                else:
                    relationship = part
                    columns = []

                attr = getattr(previous_model, relationship)

                if result:
                    result = result.joinedload(attr)
                else:
                    result = joinedload(attr)

                if columns:
                    result = result.load_only(*columns)
                else:
                    result = result.load_only("id")

                if i < len(parts) - 1:
                    previous_model = attr.property.mapper.class_

            return result

        options = []
        for relation in relations:
            options.append(process_relation(relation))

        return options

    def get_key_parts(self, key):
        # print(key)
        subpart_start_idx = key.find(".[")
        last_index_of_parts1 = len(key)
        if subpart_start_idx >= 0:
            last_index_of_parts1 = subpart_start_idx
        result = key[:last_index_of_parts1].split(".")
        if subpart_start_idx >= 0:
            subpart_keys = regex.sub(
                r"(?<=\[[^\]]*),(?=[^\[]*\])",
                ";",
                key[subpart_start_idx + 2 : len(key) - 1],
            ).split(",")
            all_sub_parts = []

            for i in range(0, len(subpart_keys)):
                subpart = self.get_key_parts(regex.sub(";", ",", subpart_keys[i]))
                # if len(subpart) == 1:
                #     subpart = subpart[0]
                all_sub_parts.append(subpart)

            if len(all_sub_parts) > 0:
                result.append(all_sub_parts)

        return result

    def get_cond_reccur(self, attrs, condition_operator: Any = and_):
        attrs.reverse()
        cond = None
        for i in range(0, len(attrs)):
            if isinstance(attrs[i], list):
                conditions = []
                for sub in attrs[i]:
                    conditions.append(self.get_cond_reccur(sub))
                cond = condition_operator(*conditions)
            else:
                attr, all = attrs[i]
                if i == 0:
                    cond = attr
                else:
                    try:
                        cond = attr.has(cond)
                    except:
                        cond = attr.any(cond)
                        if all:
                            cond = ~cond
        return cond

    def get_condition_deep_multiple(self, db, condition, current_user=None):
        key = condition.get("key", None)
        value = condition.get("value", None)
        operator = condition.get("operator", None)
        # key, value, operator must be an array and with same length
        cond_arr_sql = []
        if isinstance(key, list):
            for i in range(len(key)):
                current_cond = {
                    "key": key[i],
                    "operator": operator[i],
                    "value": value[i] if value else None,
                }
                cond_sql = self.sub_get_condition_deep_multiple(
                    db=db, condition=current_cond, current_user=current_user
                )
                cond_arr_sql.append(cond_sql)
            return or_(*cond_arr_sql)
        else:
            return self.sub_get_condition_deep_multiple(
                db=db, condition=condition, current_user=current_user
            )

    def sub_get_condition_deep_multiple(self, db, condition, current_user=None):
        keys = self.get_key_parts(condition["key"])
        # print(keys)
        operators = condition["operator"].split(",")
        values = [condition.get("value", None)]
        match = condition.get("match", "and")
        if len(operators) > 1 and values[0]:
            if "[[" in values[0]:
                values = json.loads(values[0])
            else:
                values = str(values[0]).split(",")
        condition_operator = or_ if match == "or" else and_
        current_idx = {"value": 0}
        attrs = self.get_attrs(self.model, current_idx, keys, operators, values)
        cond = self.get_cond_reccur(attrs=attrs, condition_operator=condition_operator)
        return cond

    def getStringDateTimeFormat(self, date_string):
        if len(date_string.split(" ")) >= 2:
            return date_string
        else:
            parsed_date = datetime.strptime(date_string, "%Y-%m-%d")
            formatted_string = parsed_date.strftime("%Y-%m-%d %H:%M")
        return formatted_string

    def get_attrs(self, parent_model, current_idx, keys, operators, values):
        previous_model = parent_model
        attrs = []
        for i in range(0, len(keys)):
            if i < len(keys) - 1:
                key_temp = keys[i]
                all = False
                if key_temp.startswith("~"):
                    key_temp = key_temp[1:]
                    all = True
                attr = getattr(previous_model, key_temp)
                previous_model = attr.property.mapper.class_
                attrs.append((attr, all))
            else:
                if isinstance(keys[i], str):
                    idx = current_idx["value"]
                    operator = operators[idx]
                    value = values[idx]
                    filter_condition = None

                    attribute = None
                    if keys[i].startswith("@"):
                        method_name = keys[i]
                        args = value["args"]
                        value = value["operator_value"]
                        method = getattr(previous_model, method_name.replace("@", ""))
                        attribute = method(*args)
                    else:
                        attribute = getattr(previous_model, keys[i])

                    if operator == "==":
                        filter_condition = attribute == value
                    elif operator == "!=":
                        filter_condition = attribute != value
                    elif operator == "<=":
                        filter_condition = attribute <= value   
                    elif operator == ">=":
                        filter_condition = attribute >= value
                    elif operator == ">":
                        filter_condition = attribute > value
                    elif operator == "<":
                        filter_condition = attribute < value
                    elif operator == "like":
                        filter_condition = attribute.like("%" + value + "%")
                    elif operator == "month":
                        if value is None:
                            filter_condition = extract("month", attribute).is_(None)
                        else:
                            filter_condition = extract("month", attribute) == value
                    elif operator == "date":
                        date_value = datetime.strptime(value, "%Y-%m-%d").date()
                        filter_condition = func.date(attribute) == date_value
                    elif operator == "lte_date":
                        date_value = datetime.strptime(value, "%Y-%m-%d").date()
                        filter_condition = func.date(attribute) <= date_value
                    # elif operator == "last_24h":
                    #     twenty_four_hours_ago = datetime.now() - timedelta(hours=24)
                    #     filter_condition = (
                    #         getattr(previous_model,
                    #                 keys[i]) >= twenty_four_hours_ago
                    #     )
                    elif operator == "last_24h":
                        now = datetime.now()
                        twenty_four_hours_ago = now - timedelta(hours=24)

                        filter_condition = attribute.between(
                            twenty_four_hours_ago, now
                        )

                    elif operator == "between_date":
                        date_split = value.split(",")
                        date_1 = datetime.strptime(
                            self.getStringDateTimeFormat(date_split[0]),
                            "%Y-%m-%d %H:%M",
                        ).date()
                        date_2 = datetime.strptime(
                            self.getStringDateTimeFormat(date_split[1]),
                            "%Y-%m-%d %H:%M",
                        ).date()
                        filter_condition = func.date(attribute).between(date_1, date_2)
                    elif operator == "year":
                        if value is None:
                            filter_condition = extract("year", attribute).is_(None)
                        else:
                            filter_condition = extract("year", attribute) == value
                    elif operator == "lower_or_equal_year":
                        if value is None:
                            filter_condition = extract("year", attribute).is_(None)
                        else:
                            filter_condition = extract("year", attribute) <= value
                    elif operator == "greater_or_equal_year":
                        if value is None:
                            filter_condition = extract("year", attribute).is_(None)
                        else:
                            filter_condition = extract("year", attribute) >= value
                    elif operator == "week":
                        if value is None:
                            filter_condition = extract("week", attribute).is_(None)
                        else:
                            filter_condition = extract("week", attribute) == value
                    elif operator == "isNull":
                        filter_condition = attribute.is_(None)
                    elif operator == "isNotNull":
                        filter_condition = attribute.isnot(None)
                    elif operator == "notIn":
                        filter_condition = attribute.notin_(value)
                    elif operator == "in":
                        filter_condition = attribute.in_(value)
                    elif operator.startswith("json."):
                        json_key = "$." + operator.split(".")[1]
                        col_value = func.json_extract(
                            func.json_unquote(attribute), json_key
                        )
                        if value == "isNull":
                            filter_condition = or_(
                                col_value.is_(None),
                                col_value.is_(False),
                                col_value.like("false"),
                            )
                        elif value == "isNotNull":
                            filter_condition = col_value.isnot(None)
                        else:
                            filter_condition = col_value.like(value)
                    elif operator == "ratio":
                        filter_condition = func.levenshtein_ratio(
                            func.upper(attribute), func.upper(value[0])
                        ) > (value[1] / 100)
                    idx += 1
                    current_idx["value"] = idx
                    attrs.append((filter_condition, False))
                else:
                    same_level = []
                    for key in keys[i]:
                        same_level.append(
                            self.get_attrs(
                                previous_model, current_idx, key, operators, values
                            )
                        )
                    attrs.append(same_level)

        return attrs

    def get_first_where_array(
        self, db: Session, *, where: Any = None, relations=None, current_user=None
    ) -> ModelType:
        query = db.query(self.model)
        if where is not None:
            conditions = []
            for condition in where:
                filter_condition = self.get_condition_deep_multiple(
                    db=db, condition=condition, current_user=current_user
                )
                if filter_condition is not None:
                    conditions.append(filter_condition)
            query = query.filter(and_(*conditions))
            if relations is not None and len(relations) > 0:
                query = query.options(*(self.get_all_relations(relations)))

        result = query.first()

        return result

    def get_multi_where_array(
        self,
        db: Session,
        *,
        skip: int = 0,
        limit: int = 100,
        order_by: str = "id",
        order_by_subquery=None,
        where: Any = None,
        order: str = "DESC",
        relations=None,
        current_user=None,
        include_deleted: bool = False,
    ):
        query = db.query(self.model)
        conditions = self.get_full_condition(
            db=db,
            where=where,
            current_user=current_user,
            include_deleted=include_deleted,
        )
        if conditions is not None:
            query = query.filter(conditions)

        order_function = asc
        if order == "DESC":
            order_function = desc

        if order_by_subquery is not None:
            query = query.order_by(order_function(order_by_subquery))
        else:
            if len(order_by.split(".")) > 1:
                order_by_subquery = self.get_order_by_subquery(
                    db=db, order_by_key=order_by
                )
                query = query.order_by(order_function(order_by_subquery))
                # print(query)
            else:
                order_by_attribute = getattr(self.model, order_by)
                query = query.order_by(order_function(order_by_attribute))

        query = (
            query.order_by(
                desc(getattr(self.model, "id")),
            )
            .offset(skip)
            .limit(limit)
        )

        if relations is not None and len(relations) > 0:
            query = query.options(*(self.get_all_relations(relations)))

        result = query.all()
        return result

    def get_multi_where_array_v2(
        self,
        db: Session,
        *,
        skip: int = 0,
        limit: int = 100,
        no_limit: bool = False,
        order_by: str = "id",
        where: Any = None,
        order: str = "DESC",
        base_columns=None,
        relations=None,
        current_user=None,
        include_deleted: bool = False,
        order_by_subquery=None,
        today_first: bool = False,
    ) -> List[ModelType]:
        query = db.query(self.model)
        conditions = self.get_full_condition(
            db=db,
            where=where,
            current_user=current_user,
            include_deleted=include_deleted,
        )
        if conditions is not None:
            query = query.filter(conditions)

        order_function = asc
        if order == "DESC":
            order_function = desc

        if order_by_subquery is not None:
            query = query.order_by(order_function(order_by_subquery))
        else:
            today = date.today()
            if len(order_by.split(".")) > 1:
                if today_first:
                    order_by_subquery = self.get_order_by_subquery(
                        db=db, order_by_key=order_by
                    )
                    query = query.order_by(
                        case([(func.date(order_by_subquery) == today, 0)], else_=1),
                        order_function(order_by_subquery),
                    )
                else:
                    order_by_subquery = self.get_order_by_subquery(
                        db=db, order_by_key=order_by
                    )
                    query = query.order_by(order_function(order_by_subquery))
            else:
                if today_first:
                    order_by_attribute = getattr(self.model, order_by)

                    query = query.order_by(
                        case([(func.date(order_by_attribute) == today, 0)], else_=1),
                        order_function(order_by_attribute),
                    )
                else:
                    order_by_attribute = getattr(self.model, order_by)
                    query = query.order_by(order_function(order_by_attribute))

        query = (
            query.order_by(
                desc(getattr(self.model, "id")),
            )
            .offset(skip)
        )
        if not no_limit:
            query = query.limit(limit)
        if base_columns is not None and len(base_columns) > 0:
            query = query.options(load_only(*base_columns))

        if relations is not None and len(relations) > 0:
            load_options = self.get_joined_load_v2(relations)
            query = query.options(*load_options)
        
        compiled_query = str(query.statement.compile(compile_kwargs={"literal_binds": True}))
        # print(compiled_query)
   
        result = query.all()
        return result

    def get_order_by_subquery(self, db: Session, *, order_by_key):
        key_segments = order_by_key.split(".")
        subquery_filter = True
        attribute = getattr(self.model, key_segments[0])
        joins = []
        last_attribut = self.model
        first_attribut = attribute.property.mapper.class_
        if len(key_segments) > 1:
            for segment in key_segments[1:]:
                last_attribut = attribute.property.mapper.class_

                if segment.startswith("@"):
                    pattern = r"@(\w+)(\(.*\))"
                    match = re.search(pattern, segment)
                    method_name = match.group(1)
                    argument = match.group(2)
                    method = getattr(attribute.property.mapper.class_, method_name)
                    attribute = eval("method" + argument)
                else:
                    attribute = getattr(attribute.property.mapper.class_, segment)

                if segment != key_segments[-1]:
                    joins.insert(0, attribute)

        model_mapper = inspect(first_attribut)

        for relationship in model_mapper.relationships:

            if relationship.mapper.class_ == self.model:
                foreign_key_column = list(relationship.local_columns)[0]
                subquery_filter = foreign_key_column == self.model.id
                break

        subquery_query = db.query(attribute).select_from(last_attribut)
        for relation in joins:
            subquery_query = subquery_query.join(relation)
        subquery = subquery_query.filter(subquery_filter).limit(1).as_scalar()
        return subquery

    def get_multi(
        self,
        db: Session,
        *,
        skip: int = 0,
        limit: int = 100,
        order_by: str = "id",
        where: Any = None,
        order: str = "DESC",
        relations=None,
    ) -> List[ModelType]:
        wheres = []
        if where is not None and where != "":
            wheres = ast.literal_eval(where)
        return self.get_multi_where_array(
            db,
            skip=skip,
            limit=limit,
            order_by=order_by,
            where=wheres,
            order=order,
            relations=relations,
        )

    def get_multi_v2(
        self,
        db: Session,
        *,
        skip: int = 0,
        limit: int = 100,
        order_by: str = "id",
        where: Any = None,
        order: str = "DESC",
        base_columns=None,
        relations=None,
    ) -> List[ModelType]:
        wheres = []
        if where is not None and where != "":
            wheres = ast.literal_eval(where)
        return self.get_multi_where_array_v2(
            db,
            skip=skip,
            limit=limit,
            order_by=order_by,
            where=wheres,
            order=order,
            relations=relations,
            base_columns=base_columns,
        )

    def create(
        self,
        db: Session,
        *,
        obj_in: CreateSchemaType,
        user_id: int = None,
        commit: bool = True,
        refresh: bool = True,
        flush: bool = False
    ) -> ModelType:
        obj_in_data = jsonable_encoder(obj_in)
        
        # Filter out any keys that are not in the model
        model_columns = self.model.__table__.columns.keys()
        filtered_data = {k: v for k, v in obj_in_data.items() if k in model_columns}
        
        db_obj = self.model(**filtered_data)
        db_obj.updated_at = func.now()
        db_obj.last_user_to_interact = user_id
        db.add(db_obj)
        if commit:
            db.commit()
        if flush:
            db.flush()
        if refresh:
            db.refresh(db_obj)
        
        return db_obj

    def create_multi(
        self,
        db: Session,
        *,
        objs_in: List[CreateSchemaType],
        user_id: int = None,
        commit: bool = True,
    ) -> List[ModelType]:
        objs_to_add = []
        for obj_in in objs_in:
            obj_in_data = jsonable_encoder(obj_in)
            # Filter out any keys that are not in the model
            model_columns = self.model.__table__.columns.keys()
            filtered_data = {k: v for k, v in obj_in_data.items() if k in model_columns}
            db_obj = (
                self.model(**filtered_data)
                if not user_id
                else self.model(**filtered_data, last_user_to_interact=user_id)
            )  # type: ignore
            db_obj.updated_at = func.now()
            objs_to_add.append(db_obj)
        db.add_all(objs_to_add)
        if commit:
            db.commit()
            for obj in objs_to_add:
                db.refresh(obj)
        return objs_to_add

    def add_model(
        self,
        db: Session,
        *,
        db_obj: ModelType,
        user_id: Optional[int] = None,
        commit: bool = True,
    ) -> ModelType:
        if user_id:
            db_obj.last_user_to_interact = user_id
        db.add(db_obj)
        if commit:
            db.commit()
            db.refresh(db_obj)
        return db_obj

    def update(
        self,
        db: Session,
        *,
        db_obj: ModelType,
        obj_in: Union[UpdateSchemaType, Dict[str, Any]],
        user_id: int = None,
        commit: bool = True,
        flush: bool = False
    ) -> ModelType:
        obj_data = custom_jsonable_encoder(db_obj)
        # if "created_at" in jsonable_encoder(obj_in):
        #     obj_in.created_at = db_obj.created_at
        if isinstance(obj_in, dict):
            update_data = obj_in
        else:
            update_data = obj_in.dict(exclude_unset=True)
        # print("update_data",update_data)
        update_data = (
            {
                **update_data,
                "last_user_to_interact": user_id,
            }
            if user_id
            else {**update_data}
        )

        for field in obj_data:
            if field in update_data:
                setattr(db_obj, field, update_data[field])
        db_obj.updated_at = func.now()
        db.add(db_obj)
        if commit:
            db.commit()
            db.refresh(db_obj)
        if flush:
            db.flush()
            db.refresh(db_obj)
        return db_obj

    def remove(self, db: Session, *, id: int, commit: bool = True) -> ModelType:
        obj = db.query(self.model).get(id)
        db.delete(obj)
        if commit:
            db.commit()
        return obj

    def remove_or_soft(
        self,
        db: Session,
        *,
        id: int,
        user_id: int = None,
        commit: bool = True,
        soft: bool = True,
    ) -> Any:
        if soft:
            self.soft_delete(db=db, id=id, commit=commit, user_id=user_id)
        else:
            self.remove(db=db, id=id, commit=commit)
        if commit:
            db.commit()

    def remove_or_soft_or_restore(
        self,
        db: Session,
        *,
        id: int,
        user_id: int = None,
        commit: bool = True,
        operation: str = "soft_delete",
    ) -> Any:
        if operation == "soft_delete":
            self.soft_delete(db=db, id=id, commit=commit, user_id=user_id)
        elif operation == "restore_deleted":
            self.restore_deleted(db=db, id=id, commit=commit, user_id=user_id)
        elif operation == "remove":
            self.remove(db=db, id=id, commit=commit)
        else:
            raise ValueError(f"Invalid operation {operation}")
        if commit:
            db.commit()

    def bulk_remove(
        self, db: Session, *, ids_to_delete: str, commit: bool = True, keys: str = "id"
    ) -> ModelType:
        ids_to_select = [int(x) for x in ast.literal_eval(ids_to_delete)]
        query = (
            db.query(getattr(self.model, keys))
            .filter(getattr(self.model, keys).in_(ids_to_select))
            .all()
        )
        ids_found = [result[0] for result in query]
        # to delete only the IDs that exist in the database
        query = delete(self.model).where(getattr(self.model, keys).in_(ids_found))
        db.execute(query)
        if commit:
            db.commit()

    def soft_delete(
        self, db: Session, *, id: int, commit: bool = True, user_id: int = None
    ) -> ModelType:
        db_obj = db.query(self.model).get(id)
        obj_data = jsonable_encoder(db_obj)
        update_data = (
            {
                "deleted_at": datetime.utcnow(),
                # "deleted_by_user_id": user_id,
                "last_user_to_interact": user_id,
            }
            if user_id
            else {"deleted_at": datetime.utcnow()}
        )

        for field in obj_data:
            if field in update_data:
                setattr(db_obj, field, update_data[field])
        db.add(db_obj)
        if commit:
            db.commit()
            db.refresh(db_obj)
        return db_obj

    def restore_deleted(
        self, db: Session, *, id: int, commit: bool = True, user_id: int = None
    ) -> ModelType:
        db_obj = db.query(self.model).get(id)
        obj_data = jsonable_encoder(db_obj)
        update_data = (
            {
                "deleted_at": None,
                "last_user_to_interact": user_id,
            }
            if user_id
            else {"deleted_at": None}
        )

        for field in obj_data:
            if field in update_data:
                setattr(db_obj, field, update_data[field])
        db.add(db_obj)
        if commit:
            db.commit()
            db.refresh(db_obj)
        return db_obj

    def get_count_where_array(
        self,
        db: Session,
        where: Any = None,
        current_user=None,
        include_deleted=False,
    ) -> int:
        query = db.query(self.model.id)
        conditions = self.get_full_condition(
            db=db,
            where=where,
            current_user=current_user,
            include_deleted=include_deleted,
        )
        if conditions is not None:
            query = query.filter(conditions)

        result = query.count()
        return result

    def get_full_condition(
        self, db: Session, where: Any = None, current_user=None, include_deleted=False
    ) -> Any:
        if not include_deleted:
            if not where:
                where = []
            if all(key != "deleted_at" for key in where):
                where.append(
                    {
                        "key": "deleted_at",
                        "operator": "isNull",
                    }
                )
        if where is not None:
            conditions = []
            is_or = False
            for parent_condition in where:
                if type(parent_condition) is list:
                    is_or = True
                    temp_conditions = []
                    for condition in parent_condition:
                        filter_condition = self.get_condition_deep_multiple(
                            db=db, condition=condition, current_user=current_user
                        )
                        if filter_condition is not None:
                            temp_conditions.append(filter_condition)
                    conditions.append(and_(*temp_conditions))
                else:
                    filter_condition = self.get_condition_deep_multiple(
                        db=db, condition=parent_condition, current_user=current_user
                    )
                    if filter_condition is not None:
                        conditions.append(filter_condition)
            query_operator = or_ if is_or else and_
            return query_operator(*conditions)
        else:
            return None

    def remove_where_array(
        self, db: Session, where: Any = None, commit: bool = True
    ) -> int:
        query = db.query(self.model)
        if where is not None:
            conditions = []
            for condition in where:
                filter_condition = self.get_condition_deep_multiple(
                    db=db, condition=condition
                )
                if filter_condition is not None:
                    conditions.append(filter_condition)

            query = query.filter(and_(*conditions))
        
        # Delete records and track event
        records_to_delete = query.all()
        for record in records_to_delete:
            db.delete(record)
        if commit:
            db.commit()

    def get_where_array(
        self,
        db: Session,
        where: Any = None
    ) -> int:
        query = db.query(self.model)
        if where is not None:
            conditions = []
            for condition in where:
                filter_condition = self.get_condition_deep_multiple(
                    db=db, condition=condition
                )
                if filter_condition is not None:
                    conditions.append(filter_condition)

            query = query.filter(and_(*conditions))
        data = query.all()
        return data
    
    def get_first_where_array_v2(
        self,
        db: Session,
        base_columns=None,
        relations=None,
        where: Any = None
    ) -> ModelType:
        query = db.query(self.model)
        if where is not None:
            conditions = []
            for condition in where:
                filter_condition = self.get_condition_deep_multiple(
                    db=db, condition=condition
                )
                if filter_condition is not None:
                    conditions.append(filter_condition)

            query = query.filter(and_(*conditions))
        if base_columns is not None and len(base_columns) > 0:
            query = query.options(load_only(*base_columns))

        if relations is not None and len(relations) > 0:
            load_options = self.get_joined_load_v2(relations)
            query = query.options(*load_options)
        data = query.first()
        return data

    def get_count(self, db: Session, where: Any = None) -> int:
        wheres = []
        if where is not None and where != "":
            wheres = ast.literal_eval(where)
        return self.get_count_where_array(db, where=wheres)

    def get_date_value(self, interval: str = "year") -> Any:
        start_date = datetime.now(datetime.UTC).replace(
            month=1, day=1, hour=0, minute=0, second=0, microsecond=0
        )
        end_date = start_date + timedelta(days=365)
        if interval == "day":
            start_date = datetime.now(datetime.UTC).replace(
                hour=0, minute=0, second=0, microsecond=0
            )
            end_date = start_date + timedelta(days=1)
        elif interval == "week":
            start_date = datetime.now(datetime.UTC).replace(
                hour=0, minute=0, second=0, microsecond=0
            ) - timedelta(days=datetime.now(datetime.UTC).weekday())
            end_date = start_date + timedelta(weeks=1)
        elif interval == "month":
            start_date = datetime.now(datetime.UTC).replace(
                day=1, hour=0, minute=0, second=0, microsecond=0
            )
            end_date = start_date + timedelta(days=32)
        return start_date, end_date

    def get_query_filter(self, to_compare_date: Any, previous_model=None):
        start_day, end_day = self.get_date_value("day")
        start_week, end_week = self.get_date_value("week")
        start_month, end_month = self.get_date_value("month")
        start_year, end_year = self.get_date_value("year")
        if previous_model:
            day = func.coalesce(
                func.count(
                    case(
                        [
                            (
                                and_(
                                    to_compare_date >= start_day,
                                    to_compare_date < end_day,
                                ),
                                getattr(previous_model, "id"),
                            )
                        ],
                        else_=0,
                    )
                ),
                0,
            ).label("day")

            week = func.coalesce(
                func.count(
                    case(
                        [
                            (
                                and_(
                                    to_compare_date >= start_week,
                                    to_compare_date < end_week,
                                ),
                                getattr(previous_model, "id"),
                            )
                        ],
                        else_=0,
                    )
                ),
                0,
            ).label("week")

            month = func.coalesce(
                func.count(
                    case(
                        [
                            (
                                and_(
                                    to_compare_date >= start_month,
                                    to_compare_date < end_month,
                                ),
                                getattr(previous_model, "id"),
                            )
                        ],
                        else_=0,
                    )
                ),
                0,
            ).label("month")

            year = func.coalesce(
                func.count(
                    case(
                        [
                            (
                                and_(
                                    to_compare_date >= start_year,
                                    to_compare_date < end_year,
                                ),
                                getattr(previous_model, "id"),
                            )
                        ],
                        else_=0,
                    )
                ),
                0,
            ).label("year")

            return day, week, month, year
        else:
            day = func.coalesce(
                func.sum(
                    func.if_(
                        and_(to_compare_date >= start_day, to_compare_date < end_day),
                        1,
                        0,
                    )
                ),
                0,
            ).label("day")

            week = func.coalesce(
                func.sum(
                    func.if_(
                        and_(to_compare_date >= start_week, to_compare_date < end_week),
                        1,
                        0,
                    )
                ),
                0,
            ).label("week")

            month = func.coalesce(
                func.sum(
                    func.if_(
                        and_(
                            to_compare_date >= start_month, to_compare_date < end_month
                        ),
                        1,
                        0,
                    )
                ),
                0,
            ).label("month")

            year = func.coalesce(
                func.sum(
                    func.if_(
                        and_(to_compare_date >= start_year, to_compare_date < end_year),
                        1,
                        0,
                    )
                ),
                0,
            ).label("year")
            return day, week, month, year

    def get_date_range_filter(
        self,
        previous_model,
        keys,
        date_range,
        month=None,
        year=None,
        date_from: date = None,
        date_to: date = None,
    ):
        today = datetime.today()
        if date_range == "today":
            start_day = today.replace(hour=0, minute=0, second=0, microsecond=0)
            end_day = start_day + timedelta(days=1)
        elif date_range == "this_week":
            # start of the week (Monday)
            start_day = today - timedelta(days=today.weekday())
            start_day = start_day.replace(hour=0, minute=0, second=0, microsecond=0)
            end_day = start_day + timedelta(days=7)
        elif date_range == "specific_month" and month:
            year = year or today.year
            start_day = datetime(year, month, 1, 0, 0, 0, 0)
            if month == 12:
                end_day = start_day.replace(year=year + 1, month=1)
            else:
                end_day = start_day.replace(month=month + 1)
        elif date_range == "specific_year" and year:
            start_day = datetime(year, 1, 1, 0, 0, 0, 0)
            end_day = datetime(year + 1, 1, 1, 0, 0, 0, 0)
        elif date_range == "between" and date_from and date_to:
            start_day = date_from
            end_day = date_to
        else:
            start_day = today.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            next_month = (today.month % 12) + 1
            if next_month == 1:
                end_day = today.replace(
                    year=today.year + 1,
                    month=next_month,
                    day=1,
                    hour=0,
                    minute=0,
                    second=0,
                    microsecond=0,
                )
            else:
                end_day = today.replace(
                    month=next_month, day=1, hour=0, minute=0, second=0, microsecond=0
                )

        return and_(
            getattr(previous_model, keys) >= start_day,
            getattr(previous_model, keys) < end_day,
        )

    def get_subscription_by_id_with_number_of_simulation(self, db: Session, id: int):
        try:
            _filter = [
                Subscription.id == id,
                Subscription.disabled_at.is_(None),
                Subscription.deleted_at.is_(None),
                Subscription.subscription_status == SubscriptionStatusEnum.ACTIVE,
            ]

            query_subscription = (
                db.query(Subscription)
                .filter(and_(*_filter))
                # .options(joinedload(Subscription.simulation))
            )

            query_number_of_simulation = (
                db.query(func.count(Simulation.id))
                .filter(
                    and_(
                        *[
                            Simulation.deleted_at.is_(None),
                            Simulation.subscription_id == id,
                            extract("year", Simulation.created_at)
                            == datetime.now().year,
                            extract("month", Simulation.created_at)
                            == datetime.now().month,
                        ]
                    )
                )
            )

            return query_subscription.one(), query_number_of_simulation.scalar()
        except Exception as _e:
            raise HTTPException(
                status_code=404,
                detail="Subscription not found when creating a subscription user",
            )

    def get_product_by_id(self, db: Session, id: int):
        try:
            return db.query(Product.id).filter(Product.id == id).one()
        except Exception as e:
            raise HTTPException(
                status_code=404,
                detail="Subscription not found when creating a subscription user",
            )
