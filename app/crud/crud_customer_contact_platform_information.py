from app.crud.base import CRUDBase
from app.models.customer_contact_platform_information import CustomerContactPlatformInformation

from app.schemas.customer_contact_platform_information import CustomerContactPlatformInformationCreate, CustomerContactPlatformInformationUpdate


class CRUDCustomerContactPlatformInformation(CRUDBase[CustomerContactPlatformInformation, CustomerContactPlatformInformationCreate, CustomerContactPlatformInformationUpdate]):
    def update_or_create(self, db, customer_id, contact_platform_id, value):
        contact_platform_information = self.get_first_where_array(
            db=db,
            where=[
                {"key": "customer_id", "operator": "==", "value": customer_id},
                {"key": "contact_platform_id", "operator": "==", "value": contact_platform_id}
            ]
        )
        if contact_platform_information:
            return self.update(db=db, db_obj=contact_platform_information, obj_in={
                "value": value
            })
        else:
            return self.create(db=db, obj_in={
                "customer_id": customer_id,
                "contact_platform_id": contact_platform_id,
                "value": value
            })


customer_contact_platform_information = CRUDCustomerContactPlatformInformation(CustomerContactPlatformInformation)
