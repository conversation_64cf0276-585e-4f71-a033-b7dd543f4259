from app.models import subscription
from app.config.db.database import Base
from app.models.invitation import Invitation
from sqlalchemy.orm import relationship, column_property
from app.enums.title_enum import TitleEnum
from app.models.subscription import Subscription
from app.enums.subscription_status_enum import SubscriptionStatusEnum
from sqlalchemy import (
    Boolean,
    Column,
    DateTime,
    Enum,
    ForeignKey,
    Integer,
    String,
    JSON,
    Text,
    and_,
    case,
    func,
    or_,
    select,
)


class Customer(Base):
    __tablename__ = "customer"
    id = Column(Integer, primary_key=True, autoincrement=True)
    first_name = Column(String(255))
    last_name = Column(String(255))
    cart_reference = Column(String(255))
    profile_image_json = Column(JSON)
    company_logo_json = Column(JSON)
    auth_user_id = Column(Integer, index=True)
    email = Column(String(255), unique=True)
    pseudo = Column(String(255), unique=True)
    title = Column(Enum(TitleEnum))
    street_address = Column(String(255))
    district_postal_code = Column(String(255))
    city = Column(String(255))
    country = Column(String(255))
    mobile_number = Column(String(255))
    accept_cgu = Column(Boolean, default=False)
    stripe_customer_id = Column(String(255), index=True)
    created_by = Column(Integer, ForeignKey("customer.id"))
    to_delete_at = Column(DateTime)
    settings_json = Column(JSON)
    default_referential_info_json = Column(JSON)
    catalogues_json = Column(JSON)
    autonomy_catalogue_json = Column(JSON)
    country_id = Column(Integer, ForeignKey("country.id"), nullable=True)
    timezone_offset = Column(String(50), nullable=True)
    removed_from_mailing_list_at = Column(DateTime, nullable=True)
    source_file_name = Column(String(500), nullable=True)
    source_row_id = Column(Integer, nullable=True)
    unsubscribed_at = Column(DateTime)

    country_rel = relationship(
        "Country", foreign_keys=[country_id],
    )

    full_name = column_property(
        case(
            [
                (
                    and_(
                        func.coalesce(first_name, "") != "",
                        func.coalesce(last_name, "") != "",
                    ),
                    func.trim(
                        func.concat(
                            func.trim(first_name), " ",
                            func.trim(last_name),
                        )
                    ),
                )
            ],
            else_="",
        )
    )

    account_information = relationship(
        "AccountInformation", back_populates="customer", uselist=False
    )

    subscription = relationship(
        "Subscription",
        back_populates="customer",
        uselist=False,
        primaryjoin=and_(
            Subscription.customer_id == id,
            Subscription.subscription_status == SubscriptionStatusEnum.ACTIVE,
            # Subscription.expired_date >= func.now(),
            Subscription.start_date <= func.now(),
        ),
    )
    subscriptions = relationship(
        "Subscription",
        back_populates="customer", 
        uselist=True,
    )
    
    current_subscription_id = column_property(
        select([Subscription.id])
        .where(
            and_(
                Subscription.customer_id == id,
                Subscription.subscription_status == SubscriptionStatusEnum.ACTIVE
            )
        )
        .limit(1)
        .correlate_except(Subscription)
    )

    invitation_id = column_property(
        select([Invitation.id])
        .where(Invitation.customer_id == id)
        .limit(1)
        .correlate_except(Invitation)
    )
